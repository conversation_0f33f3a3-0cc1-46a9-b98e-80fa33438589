from .extractor import ContentExtractor
from .text_cleaner import TextCleaner
from .keyword_extractor import KeywordExtractor
# Create singleton instances
text_cleaner = TextCleaner()
extractor = ContentExtractor()
keyword_extractor = KeywordExtractor()
file_handler = None  # Will be initialized with app config

__all__ = [
    'ContentExtractor',
    'FileHandler',
    'extractor',
    'file_handler',
    'text_cleaner',
    'keyword_extractor'
]

