from flask import (Blueprint, render_template, request, redirect,
                 url_for, session, flash, jsonify)
from flask_login import login_required
from auth.user_auth import UserAuth
from auth.user import get_user_annotation_mode
import logging
from cache.auth_cache import (cache_user_role, get_cached_user_role,
                             invalidate_all_user_caches)

user_auth = UserAuth()
user_routes = Blueprint('user_routes', __name__)
logger = logging.getLogger('user_routes')

@user_routes.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Handle password change requests"""
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        # Validate input fields
        if not all([current_password, new_password, confirm_password]):
            flash('Please fill in all required fields', 'danger')
            return render_template('change_password.html', user=session)
        if new_password != confirm_password:
            flash('New passwords do not match', 'danger')
            return render_template('change_password.html', user=session)

        # Attempt password change
        success, message = user_auth.change_password(session['username'], current_password, new_password)
        if not success:
            flash(f'Error changing password: {message}', 'danger')
            return render_template('change_password.html', user=session)

        # Handle successful password change
        try:
            invalidate_all_user_caches(session['username'])
            logger.info(f"Invalidated caches for user {session['username']} after password change")
        except Exception as cache_error:
            logger.warning(f"Error invalidating user caches: {str(cache_error)}")

        flash('Password changed successfully!', 'success')

        # Redirect based on user role
        role_redirects = {
            'admin': 'admin_routes.dashboard',
            'auditor': 'auditor_routes.auditor_dashboard',
            'annotator': 'annotator_routes.annotator_dashboard'
        }
        redirect_route = role_redirects.get(session.get('role'), 'annotator_routes.annotate_route')
        return redirect(url_for(redirect_route))

    return render_template('change_password.html', user=session)

@user_routes.route('/check-user-role', methods=['GET','POST'])
def check_user_role():
    # Handle GET: return current user info
    if request.method == 'GET':
        return jsonify({'username': session.get('username'), 'role': session.get('role')}), 200
    """API endpoint to check a user's role"""
    data = request.get_json()
    username = data.get('username')
    if not username:
        return jsonify({'error': 'Username is required'}), 400

    # Try cache first, then database
    role = get_cached_user_role(username)
    if role is None:
        user_data = user_auth.get_user(username)
        role = user_data.get('role', 'annotator') if user_data else 'unknown'
        cache_user_role(username, role)

    logger.debug(f"Role for user {username}: {role}")
    return jsonify({'role': role})

@user_routes.route('/no-tasks')
@login_required
def no_tasks():
    """Display when no tasks are available"""
    annotation_mode = get_user_annotation_mode(session.get('username'))
    return render_template('no_tasks.html',
                          message=f"There are currently no {annotation_mode} tasks available for you.",
                          verification_mode=(annotation_mode == 'verification'),
                          user=session)