# routes/fetch_data.py
import os
from datetime import datetime
from flask import Blueprint, render_template, request, redirect, url_for, session, jsonify, flash, send_file
from io import StringIO, BytesIO
from pathlib import Path
from flask_login import login_required
from auth.decorators import admin_required
from auth.user_auth import UserAuth
from core_modules.config import Config
# Import Telegram module
from Modules.telegram_drive_integration import DriveUploader
from Modules.telegram_fetcher import (telegram_manager, connect_to_telegram,
                                       verify_code, verify_password, check_auth,
                                       get_user_channels, disconnect, reset_session,
                                       get_channel_images, get_channel_dates,
                                       download_image, get_channel_analytics)
# Import cache manager
from cache.fetch_data_cache import (
    cache_channels, get_cached_channels, invalidate_channels_cache,
    cache_channel_images, get_cached_channel_images,
    cache_channel_dates, get_cached_channel_dates,
    cache_channel_analytics, get_cached_channel_analytics,
    cache_auth_status, get_cached_auth_status, invalidate_auth_status_cache
)

import logging

logger = logging.getLogger('fetch_data')
fetch_data = Blueprint('fetch_data', __name__)
user_auth = UserAuth()  # Using UserAuth from Modules


# Main Telegram logger
telegram_logger = logging.getLogger('telegram')

# Telegram Drive operations logger
telegram_drive_logger = logging.getLogger('telegram_drive')

# Telegram test logger
telegram_test_logger = logging.getLogger('telegram_test')



@fetch_data.route('/telegram-connect', methods=['GET', 'POST'])
@login_required
@admin_required
def telegram_connect():
    """Handle Telegram connection page and API connection"""
    if request.method == 'GET':
        # Show the connection page
        return render_template('admin/data_fetching/telegram_connect.html')
    else:  # POST request
        try:
            data = request.get_json()
            api_id = data.get('api_id')
            api_hash = data.get('api_hash')
            phone = data.get('phone')

            if not api_id or not api_hash:
                return jsonify({'error': 'API ID and API Hash are required'}), 400

            telegram_logger.info(f"Attempting to connect with phone: {phone}")
            result = telegram_manager.run_coroutine(
                connect_to_telegram(int(api_id), api_hash, phone)
            )
            telegram_logger.info(f"Connection result: {result}")
            return jsonify(result)
        except Exception as e:
            telegram_logger.error(f"Error connecting to Telegram: {str(e)}")
            return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/channels')
@login_required
@admin_required
def telegram_channels_list():
    """Show only Telegram channels list after successful connection"""
    try:
        auth_result = telegram_manager.run_coroutine(check_auth())
        if not auth_result.get('authenticated', False):
            flash('You need to connect to Telegram first')
            return redirect(url_for('fetch_data.telegram_connect'))

        return render_template('admin/data_fetching/telegram_channels_list.html')
    except Exception as e:
        logger.error(f"Error checking Telegram auth: {str(e)}")
        flash('Error checking Telegram authentication status')
        return redirect(url_for('fetch_data.telegram_connect'))

# The telegram_connect function has been merged with the route above
@fetch_data.route('/telegram/verify-code', methods=['POST'])
@login_required
@admin_required
def telegram_verify_code():
    """Verify Telegram authentication code"""
    try:
        data = request.get_json()
        code = data.get('code')

        if not code:
            return jsonify({'error': 'Verification code is required'}), 400

        telegram_logger.info("Verifying Telegram code")
        result = telegram_manager.run_coroutine(verify_code(code))
        telegram_logger.info(f"Code verification result: {result}")
        return jsonify(result)
    except Exception as e:
        telegram_logger.error(f"Error verifying code: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/verify-password', methods=['POST'])
@login_required
@admin_required
def telegram_verify_password():
    """Verify Telegram 2FA password"""
    try:
        data = request.get_json()
        password = data.get('password')

        if not password:
            return jsonify({'error': '2FA password is required'}), 400

        result = telegram_manager.run_coroutine(verify_password(password))
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error verifying password: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/check-auth')
@login_required
@admin_required
def telegram_check_auth():
    """Check Telegram authentication status"""
    try:
        # Check if refresh parameter is set
        refresh = request.args.get('refresh', '0') == '1'

        # Try to get from cache first if not refreshing
        if not refresh:
            cached_result = get_cached_auth_status()
            if cached_result is not None:
                logger.debug("Using cached Telegram auth status")
                # Only return cached result if it shows authenticated
                # This prevents a false positive cached auth status
                if cached_result.get('authenticated', False):
                    return jsonify(cached_result)
                # If cached result shows not authenticated, we'll check again
                # to make sure it's not a stale cache entry

        # If not in cache or refreshing, get from Telegram
        result = telegram_manager.run_coroutine(check_auth())

        # Only cache positive authentication results
        # This prevents caching temporary auth failures
        if result.get('authenticated', False):
            cache_auth_status(result)
        else:
            # If not authenticated, invalidate any existing cache
            invalidate_auth_status_cache()

        return jsonify(result)
    except Exception as e:
        logger.error(f"Error checking auth: {str(e)}")
        # On error, invalidate cache to prevent stale auth status
        try:
            invalidate_auth_status_cache()
        except Exception:
            pass
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/get-channels')
@login_required
@admin_required
def telegram_get_channels():
    """Get user's Telegram channels"""
    try:
        # Check if refresh parameter is set
        refresh = request.args.get('refresh', '0') == '1'

        # Try to get from cache first if not refreshing
        if not refresh:
            cached_channels = get_cached_channels()
            if cached_channels is not None:
                logger.debug("Using cached Telegram channels list")
                return jsonify({'channels': cached_channels})

        # If not in cache or refreshing, get from Telegram
        channels = telegram_manager.run_coroutine(get_user_channels())

        # Cache the result
        cache_channels(channels)

        return jsonify({'channels': channels})
    except Exception as e:
        logger.error(f"Error getting channels: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/disconnect')
@login_required
@admin_required
def telegram_disconnect():
    """Disconnect from Telegram"""
    try:
        result = telegram_manager.run_coroutine(disconnect())

        # Invalidate all Telegram caches when disconnecting
        try:
            invalidate_auth_status_cache()
            invalidate_channels_cache()
            logger.info("Invalidated Telegram caches after disconnection")
        except Exception as cache_error:
            logger.warning(f"Error invalidating Telegram caches: {str(cache_error)}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"Error disconnecting: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/reset-session')
@login_required
@admin_required
def telegram_reset_session():
    """Reset Telegram session"""
    try:
        result = telegram_manager.run_coroutine(reset_session())

        # Invalidate all Telegram caches when resetting session
        try:
            invalidate_auth_status_cache()
            invalidate_channels_cache()
            logger.info("Invalidated Telegram caches after session reset")
        except Exception as cache_error:
            logger.warning(f"Error invalidating Telegram caches: {str(cache_error)}")

        return jsonify(result)
    except Exception as e:
        logger.error(f"Error resetting session: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/get-channel-images/<int:channel_id>')
@login_required
@admin_required
def telegram_get_channel_images(channel_id):
    """Get images from a Telegram channel"""
    try:
        # Get optional date filter from query string
        date = request.args.get('date')

        # Check if refresh parameter is set
        refresh = request.args.get('refresh', '0') == '1'

        # Try to get from cache first if not refreshing
        if not refresh:
            cached_data = get_cached_channel_images(channel_id, date)
            if cached_data is not None:
                logger.debug(f"Using cached images for channel {channel_id} (date: {date if date else 'all'})")
                return jsonify(cached_data)

        # Get channel entity to include the title in response
        client = telegram_manager.get_client()
        channel = telegram_manager.run_coroutine(client.get_entity(channel_id))
        channel_title = channel.title if hasattr(channel, 'title') else 'Channel'

        # Get images
        images = telegram_manager.run_coroutine(get_channel_images(channel_id, date))

        # Cache the result
        cache_channel_images(channel_id, date, images, channel_title)

        return jsonify({'images': images, 'channel_title': channel_title})
    except Exception as e:
        logger.error(f"Error getting channel images: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/get-channel-dates/<int:channel_id>')
@login_required
@admin_required
def telegram_get_channel_dates(channel_id):
    """Get dates that have images in a channel"""
    try:
        # Check if refresh parameter is set
        refresh = request.args.get('refresh', '0') == '1'

        # Try to get from cache first if not refreshing
        if not refresh:
            cached_dates = get_cached_channel_dates(channel_id)
            if cached_dates is not None:
                logger.debug(f"Using cached dates for channel {channel_id}")
                return jsonify({'dates': cached_dates})

        # If not in cache or refreshing, get from Telegram
        dates = telegram_manager.run_coroutine(get_channel_dates(channel_id))

        # Cache the result
        cache_channel_dates(channel_id, dates)

        return jsonify({'dates': dates})
    except Exception as e:
        logger.error(f"Error getting channel dates: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/download-image', methods=['POST'])
@login_required
@admin_required
def telegram_download_image():
    """Download an image from Telegram"""
    try:
        data = request.get_json()
        message_id = data.get('message_id')
        channel_id = data.get('channel_id')
        filename = data.get('filename')  # Optional

        if not message_id or not channel_id:
            return jsonify({'error': 'Message ID and Channel ID are required'}), 400

        result = telegram_manager.run_coroutine(
            download_image(message_id, channel_id, filename)
        )
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error downloading image: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/images')
@login_required
@admin_required
def telegram_images():
    """Show Telegram images browser page"""
    # Check if channel_id is provided
    channel_id = request.args.get('channel_id')
    if not channel_id:
        flash('No channel selected')
        return redirect(url_for('fetch_data.telegram_channels_list'))

    return render_template('admin/data_fetching/telegram_images.html')

@fetch_data.route('/telegram/download-multiple-images', methods=['POST'])
@login_required
@admin_required
def telegram_download_multiple_images():
    """Download multiple images from Telegram"""
    try:
        data = request.get_json()
        images = data.get('images', [])

        if not images:
            return jsonify({'error': 'No images selected'}), 400

        # Get the first image's channel ID to determine the channel name
        first_image = images[0]
        channel_id = first_image.get('channel_id')

        # Get channel name for the directory
        client = telegram_manager.get_client()
        channel = telegram_manager.run_coroutine(client.get_entity(channel_id))
        channel_name = channel.title if hasattr(channel, 'title') else 'Channel'

        # Create a temp directory for batch download
        import os
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        batch_dir = os.path.join("downloaded_images", f"{channel_name}_{timestamp}")
        os.makedirs(batch_dir, exist_ok=True)

        # Download each image
        downloaded_paths = []
        for img in images:
            message_id = img.get('id')
            channel_id = img.get('channel_id')

            if message_id and channel_id:
                try:
                    result = telegram_manager.run_coroutine(
                        download_image(message_id, channel_id, session_name='test_session')
                    )

                    if result.get('path'):
                        downloaded_paths.append(result.get('path'))
                except Exception as e:
                    logger.error(f"Error downloading image {message_id}: {str(e)}")

        return jsonify({
            'success': True,
            'downloaded': len(downloaded_paths),
            'directory': batch_dir,
            'paths': downloaded_paths
        })
    except Exception as e:
        logger.error(f"Error downloading multiple images: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/get-channel-analytics/<int:channel_id>')
@login_required
@admin_required  # Restrict analytics to admin users only
def telegram_get_channel_analytics(channel_id):
    """Get analytics data for a specific channel"""
    try:
        # Check if refresh parameter is set
        refresh = request.args.get('refresh', '0') == '1'

        # Try to get from cache first if not refreshing
        if not refresh:
            cached_analytics = get_cached_channel_analytics(channel_id)
            if cached_analytics is not None:
                logger.debug(f"Using cached analytics for channel {channel_id}")
                return jsonify(cached_analytics)

        # If not in cache or refreshing, get from Telegram
        analytics = telegram_manager.run_coroutine(get_channel_analytics(channel_id))

        # Cache the result
        cache_channel_analytics(channel_id, analytics)

        return jsonify(analytics)
    except Exception as e:
        logger.error(f"Error getting channel analytics: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/get-all-channels-analytics')
@login_required
@admin_required
def telegram_get_all_channels_analytics():
    """Get analytics data for all accessible Telegram channels"""
    try:
        logger.info("Request received for all channels analytics")

        # Get user email for tracking
        user_email = session.get('user_email', 'unknown_user')
        logger.info(f"User {user_email} requested analytics for all channels")

        # First get all channels
        channels = telegram_manager.run_coroutine(get_user_channels())

        if not channels:
            logger.warning("No channels found for analytics request")
            return jsonify({
                'channels': [],
                'analytics': {},
                'global_stats': {
                    'total_channels': 0,
                    'total_images': 0,
                    'earliest_date': None,
                    'latest_date': None
                }
            })

        logger.info(f"Found {len(channels)} channels for analytics processing")

        # Get analytics for each channel
        all_analytics = {}
        global_stats = {
            'total_channels': len(channels),
            'total_images': 0,
            'earliest_date': None,
            'latest_date': None,
            'processed_channels': 0,
            'failed_channels': 0
        }

        for channel in channels:
            channel_id = channel['id']
            logger.info(f"Processing analytics for channel: {channel['title']} (ID: {channel_id})")

            try:
                analytics = telegram_manager.run_coroutine(get_channel_analytics(channel_id))
                all_analytics[channel_id] = analytics

                # Track processing result
                if 'error' in analytics:
                    global_stats['failed_channels'] += 1
                    logger.warning(f"Channel {channel_id} analytics returned with error: {analytics['error']}")
                else:
                    global_stats['processed_channels'] += 1

                # Update global stats only if no error
                if 'error' not in analytics:
                    # Update total images
                    global_stats['total_images'] += analytics['total_images']

                    # Update date range
                    if analytics['first_date']:
                        if not global_stats['earliest_date'] or analytics['first_date'] < global_stats['earliest_date']:
                            global_stats['earliest_date'] = analytics['first_date']

                    if analytics['last_date']:
                        if not global_stats['latest_date'] or analytics['last_date'] > global_stats['latest_date']:
                            global_stats['latest_date'] = analytics['last_date']

            except Exception as e:
                logger.error(f"Error getting analytics for channel {channel_id}: {str(e)}")
                global_stats['failed_channels'] += 1
                # Add a placeholder with error info
                all_analytics[channel_id] = {
                    'channel': {
                        'id': channel_id,
                        'title': channel['title'],
                        'username': channel.get('username'),
                        'participants_count': channel.get('participants_count')
                    },
                    'total_images': 0,
                    'dates': [],
                    'date_image_counts': {},
                    'first_date': None,
                    'last_date': None,
                    'error': str(e)
                }

        logger.info(f"Completed analytics for {len(channels)} channels. " +
                   f"Processed: {global_stats['processed_channels']}, " +
                   f"Failed: {global_stats['failed_channels']}, " +
                   f"Total images: {global_stats['total_images']}")

        return jsonify({
            'channels': channels,
            'analytics': all_analytics,
            'global_stats': global_stats
        })
    except Exception as e:
        logger.error(f"Error getting all channels analytics: {str(e)}")
        return jsonify({
            'error': str(e),
            'channels': [],
            'analytics': {},
            'global_stats': {
                'total_channels': 0,
                'total_images': 0,
                'earliest_date': None,
                'latest_date': None,
                'error': str(e)
            }
        }), 200  # Return 200 with error in body

@fetch_data.route('/telegram/upload-to-drive', methods=['POST'])
@login_required
@admin_required
def telegram_upload_to_drive():
    """
    Unified endpoint to upload images to Google Drive.

    Handles both:
    - Single file uploads (multipart/form-data with file attachment)
    - Multiple Telegram images (JSON with image IDs to download from Telegram)

    Automatically detects the upload type based on content-type and processes accordingly.
    After successful upload to Google Drive, local copies are automatically deleted.
    """
    try:
        telegram_drive_logger.info("Starting Telegram to Drive upload process")

        # Import the delete_file function for proper logging
        from Modules.file_handler import delete_file

        downloaded_paths = []
        channel_name = 'Telegram Channel'  # Default channel name

        # Handle both single file uploads (form data) and multiple files (JSON)
        if request.content_type and 'multipart/form-data' in request.content_type:
            # SINGLE FILE UPLOAD HANDLING
            if 'file' not in request.files:
                return jsonify({'error': 'No file provided'}), 400

            file = request.files['file']
            if not file:
                return jsonify({'error': 'Invalid file'}), 400

            # Get channel name from form data
            channel_name = request.form.get('channel_name', 'Edited Images')

            # Save file temporarily
            filename = file.filename
            temp_path = os.path.join(Config.UPLOAD_FOLDER, filename)
            file.save(temp_path)
            downloaded_paths.append(temp_path)

            telegram_drive_logger.info(f"Prepared single file for upload: {filename} for channel: {channel_name}")
        else:
            # MULTIPLE FILES UPLOAD HANDLING
            data = request.get_json()
            images = data.get('images', [])
            channel_name = data.get('channel_name', 'Telegram Channel')

            if not images:
                telegram_drive_logger.error("No images selected for upload")
                return jsonify({'error': 'No images selected'}), 400

            telegram_drive_logger.info(f"Processing {len(images)} images from channel: {channel_name}")

            # Download each image from Telegram
            for img in images:
                message_id = img.get('id')
                channel_id = img.get('channel_id')

                if message_id and channel_id:
                    try:
                        result = telegram_manager.run_coroutine(
                            download_image(message_id, channel_id, session_name='test_session')
                        )

                        if result.get('path'):
                            downloaded_paths.append(result.get('path'))
                    except Exception as e:
                        telegram_drive_logger.error(f"Error downloading image {message_id}: {str(e)}")

        # Check if we have any files to upload
        if not downloaded_paths:
            telegram_drive_logger.error("Failed to download/prepare any images for upload")
            return jsonify({'error': 'Failed to download/prepare any images for upload'}), 500

        # Upload images to Google Drive
        telegram_drive_logger.info(f"Starting upload of {len(downloaded_paths)} images to Google Drive")
        drive_uploader = DriveUploader()
        result = drive_uploader.upload_telegram_images(downloaded_paths, channel_name)

        # Log success
        channel_folder_name = result.get('channel_folder_name', channel_name)
        date_folder_name = result.get('date_folder_name', 'Unknown Date')

        # Log details about the upload - consistent message for all uploads
        telegram_drive_logger.info(f"Successfully uploaded {len(result['uploaded_files'])} images to Drive folder: {channel_folder_name} in date folder {date_folder_name}")

        # Verify upload was successful by checking uploaded_files count
        if len(result['uploaded_files']) > 0:
            telegram_drive_logger.info(f"Verified successful upload of {len(result['uploaded_files'])} files. Proceeding with local file deletion.")

            # Create a mapping of filenames to their Drive IDs for verification
            uploaded_file_map = {os.path.basename(f['name']): f['id'] for f in result['uploaded_files']}

            # Delete each local file
            for local_path in downloaded_paths:
                filename = os.path.basename(local_path)

                # Verify this file was actually uploaded to Drive
                if filename in uploaded_file_map:
                    # Delete the local file
                    if delete_file(local_path):
                        telegram_drive_logger.info(f"Successfully deleted local file after upload: {local_path}")
                    else:
                        telegram_drive_logger.warning(f"Failed to delete local file: {local_path}")
                else:
                    # This file wasn't successfully uploaded, so don't delete it
                    telegram_drive_logger.warning(f"File {filename} was not found in upload results, skipping deletion")
        else:
            telegram_drive_logger.warning("No files were successfully uploaded to Drive. Skipping local file deletion.")

        # Prepare response with consistent format for both single and multiple file uploads
        response_data = {
            'success': True,
            'folder_id': result.get('channel_folder_id', ''),
            'folder_name': result.get('channel_folder_name', channel_name),
            'folder_link': result.get('folder_link', ''),
            'date_folder_id': result.get('date_folder_id', ''),
            'date_folder_name': result.get('date_folder_name', ''),
            'date_folder_link': result.get('date_folder_link', ''),
            'root_folder_id': result.get('root_folder_id', ''),
            'root_folder_name': result.get('root_folder_name', ''),
            'root_folder_link': result.get('root_folder_link', ''),
            'uploaded_files': result.get('uploaded_files', [])
        }

        return jsonify(response_data)

    except Exception as e:
        telegram_drive_logger.error(f"Error uploading to Google Drive: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fetch_data.route('/telegram/export-analytics-csv')
@login_required
@admin_required  # Restrict analytics export to admin users only
def telegram_export_analytics_csv():
    """Export analytics data as CSV file"""
    try:
        # Get all channels
        channels = telegram_manager.run_coroutine(get_user_channels())

        if not channels:
            return jsonify({'error': 'No channels available'}), 404

        # Get include_daily parameter
        include_daily = request.args.get('include_daily', 'true').lower() == 'true'

        # Create CSV content
        csv_io = StringIO()

        # Write header
        csv_io.write('Channel ID,Channel Name,Username,Participants,Total Images,Days with Images,First Date,Last Date\n')

        # Get analytics for each channel
        for channel in channels:
            channel_id = channel['id']
            try:
                analytics = telegram_manager.run_coroutine(get_channel_analytics(channel_id))

                # Get channel info
                channel_info = analytics['channel']

                # Write channel summary row
                csv_io.write(f"{channel_id},\"{channel_info['title']}\",")
                csv_io.write(f"{channel_info['username'] or ''},")
                csv_io.write(f"{channel_info['participants_count'] or 0},")
                csv_io.write(f"{analytics['total_images']},")
                csv_io.write(f"{len(analytics['dates'])},")
                csv_io.write(f"\"{analytics['first_date'] or 'N/A'}\",")
                csv_io.write(f"\"{analytics['last_date'] or 'N/A'}\"\n")

            except Exception as e:
                logger.error(f"Error getting analytics for channel {channel_id}: {str(e)}")
                # Skip this channel but continue with others
                continue

        # Add daily breakdown if requested
        if include_daily:
            csv_io.write('\n\nDaily Image Breakdown\n')
            csv_io.write('Channel ID,Channel Name,Date,Image Count\n')

            for channel in channels:
                channel_id = channel['id']
                try:
                    analytics = telegram_manager.run_coroutine(get_channel_analytics(channel_id))

                    # Get channel info
                    channel_info = analytics['channel']

                    # Write daily breakdown rows
                    for date, count in analytics['date_image_counts'].items():
                        csv_io.write(f"{channel_id},\"{channel_info['title']}\",\"{date}\",{count}\n")

                except Exception as e:
                    logger.error(f"Error getting daily breakdown for channel {channel_id}: {str(e)}")
                    continue

        # Prepare the file for download
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"telegram_analytics_{timestamp}.csv"

        csv_io.seek(0)
        return send_file(
            BytesIO(csv_io.getvalue().encode('utf-8')),
            as_attachment=True,
            download_name=filename,
            mimetype='text/csv'
        )

    except Exception as e:
        logger.error(f"Error exporting analytics CSV: {str(e)}")
        return jsonify({'error': str(e)}), 500
