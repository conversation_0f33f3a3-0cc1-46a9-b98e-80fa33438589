import urllib3
from datetime import datetime
from flask import Flask, render_template, redirect, url_for, session, send_from_directory, request
from flask_login import LoginManager
import traceback
from markupsafe import Markup
from flask_cors import CORS

# Import Redis connector
from cache.redis_connector import create_redis_client, set_redis_client, set_redis_enabled
from auth.user import User
from core_modules.config import Config
from core_modules.logging_config import configure_app_logging
from auth.user_auth import UserAuth
#Importing routes
from routes.auth_routes import auth_routes
from routes.admin_routes import admin_routes
from routes.auditor_routes import auditor_routes
from routes.annotator_routes import annotator_routes
from routes.user_routes import user_routes
from routes.supervision_routes import supervision_routes
from routes.fetch_data import fetch_data
from synthetic_models.synthetic_dataset import synthetic_dataset_bp
from routes.extractor_route import extractor_routes

app = Flask(__name__)
# Configure CORS for Next.js frontend
CORS(app, resources={
    r"/api/*": {"origins": ["http://localhost:3000", "http://127.0.0.1:3000"]},
    r"/*": {"origins": ["http://localhost:3000", "http://127.0.0.1:3000"]}
}, supports_credentials=True)
app.config.from_object(Config)
app.secret_key = app.config['SECRET_KEY']
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024


def nl2br_filter(s):
    """
    Filter that converts newlines to <br> tags
    Args:
        s: String to convert
    Returns:
        Markup: HTML-safe string with newlines converted to <br> tags
    """
    if not s:
        return s
    return Markup(s.replace('\n', ' '))

# Add custom Jinja2 filters
@app.template_filter('format_datetime')
def format_datetime(value, format='%Y-%m-%d %H:%M'):
    """Format a datetime object or ISO datetime string."""
    if value is None:
        return ''
    if isinstance(value, str):
        # If it's already a string, just return the first part of it
        # This handles ISO format strings from cache
        if ' ' in value:
            # If it has a space, it's probably a datetime with time
            return value[:16]  # Return YYYY-MM-DD HH:MM
        else:
            # Otherwise just return the date part
            return value[:10]  # Return YYYY-MM-DD
    # If it's a datetime object, format it
    try:
        return value.strftime(format)
    except:
        return str(value)

# Register the nl2br filter
app.jinja_env.filters['nl2br'] = nl2br_filter

# Add hasattr function to Jinja2 globals
app.jinja_env.globals['hasattr'] = hasattr

# Register blueprints with API prefix for Next.js frontend
app.register_blueprint(auth_routes, url_prefix='/api/auth')
app.register_blueprint(annotator_routes, url_prefix='/api/annotator')
app.register_blueprint(admin_routes, url_prefix='/api/admin')
app.register_blueprint(auditor_routes, url_prefix='/api/auditor')
app.register_blueprint(user_routes, url_prefix='/api/users')
app.register_blueprint(supervision_routes, url_prefix='/api/supervision')
app.register_blueprint(fetch_data, url_prefix='/api/fetch-data')
app.register_blueprint(synthetic_dataset_bp, url_prefix='/api/synthetic')
app.register_blueprint(extractor_routes, url_prefix='/api/extractor')
# Disable SSL warnings - add this near the top of the file
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Set up logging for the entire application
logger = configure_app_logging(console_for_modules=True)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth_routes.login'

# Global variables and flags
current_session_folder = None

# User loader callback for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    user = user_auth.get_user_by_id(user_id)
    if not user:
        return None
    return User(user['id'], user['username'], user['role'], user.get('full_name', ''))

# Add context processor for templates
@app.context_processor
def inject_now():
    """Inject current datetime into templates"""
    return {'now': datetime.now()}

# Initialize user_auth
user_auth = UserAuth()

# Application initialization functions
def _initialize_database():
    """Initialize database tables and connections"""
    try:
        # Initialize database tables
        from databases.db_connector import initialize_database_tables
        initialize_database_tables()
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        logger.error(traceback.format_exc())
        raise  # Re-raise to be caught by the main initialization function

def _initialize_user_database():
    """Initialize user database and admin settings"""
    try:
        logger.info("Initializing user database...")

        # Initialize the user database
        user_auth._init_db()
        # Get all users
        users = user_auth.get_all_users()
        logger.info(f"Found {len(users)} users in the database")
    except Exception as e:
        logger.error(f"Error initializing user database: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def initialize_application(_):
    try:
        # PHASE 1: Initialize database
        _initialize_database()

        # PHASE 2: Initialize user database
        _initialize_user_database()

        logger.info("Application initialization completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error during application initialization: {str(e)}")
        logger.error(traceback.format_exc())
        return False

# Initialize the application
initialize_application(app)

# Initialize Redis connection - centralized here in app.py
if Config.REDIS_ENABLED:
    try:
        # Create Redis client
        redis_client = create_redis_client()

        # Set the client in the redis_connector module
        if redis_client:
            set_redis_client(redis_client)
            set_redis_enabled(True)
            logger.info("Redis connection initialized successfully")
        else:
            # If connection failed, disable Redis caching
            set_redis_enabled(False)
            logger.warning("Failed to initialize Redis connection - caching disabled")
    except Exception as e:
        # If there was an error, disable Redis caching
        set_redis_enabled(False)
        logger.warning("Redis caching disabled due to initialization error")
else:
    # Redis is disabled in configuration
    set_redis_enabled(False)
    logger.info("Redis caching disabled by configuration")

# Add Jinja2 filter for nl2br
@app.template_filter('nl2br')
def nl2br_filter_wrapper(s):
    return nl2br_filter(s)

# API route to get user session info
@app.route('/api/session')
def get_session():
    """Get current user session information"""
    if 'username' in session:
        return jsonify({
            'authenticated': True,
            'user': {
                'username': session.get('username'),
                'role': session.get('role'),
                'full_name': session.get('full_name')
            }
        })
    else:
        return jsonify({'authenticated': False})

# Legacy index route for OAuth callbacks and redirects to Next.js
@app.route('/')
def index():
    """Index route - handles OAuth callbacks and redirects to Next.js frontend"""
    # Check if this is an OAuth callback - it will have code and state/scope parameters
    auth_code = request.args.get('code')
    state = request.args.get('state')
    error = request.args.get('error')
    scope = request.args.get('scope')

    # If this has oauth parameters, check if it's a Google Drive callback
    if auth_code and (state or scope):
        logger.info(f"Detected potential OAuth callback at root URL with code={bool(auth_code)}, state={state}")

        # Import the function directly to avoid any blueprint attribute issues
        from routes.admin_routes import handle_oauth_callback

        # Call the handler function directly
        handled, response = handle_oauth_callback(auth_code, state, error, scope)
        if handled:
            logger.info("OAuth callback was handled successfully")
            return response

    # For all other requests, redirect to Next.js frontend
    return redirect('http://localhost:3000')

# Static file serving routes (these will be handled by Next.js in production)
@app.route('/pattern-bg')
def pattern_background():
    """Serve the pattern image for the landing page background"""
    return send_from_directory('static/img', 'pattern.png')

# Legacy routes - redirect to Next.js frontend
@app.route('/synthetic')
def synthetic_dataset():
    """Redirect to Next.js synthetic dataset page"""
    return redirect('http://localhost:3000/synthetic')

@app.route('/documind')
def documind():
    """Redirect to Next.js Documind page"""
    return redirect('http://localhost:3000/documind')

@app.route('/note-ocr')
def note_ocr():
    """Redirect to Next.js Note OCR page"""
    return redirect('http://localhost:3000/note-ocr')

@app.route('/coming_soon')
def coming_soon():
    """Redirect to Next.js Coming Soon page"""
    return redirect('http://localhost:3000/note-ocr/coming-soon')
# Run the application if executed as the main program
if __name__ == '__main__':
    try:
        # Run the Flask app
        app.run(host='0.0.0.0', port=8080, debug=True)
    except Exception as e:
        logger.error(f"Error during app initialization: {str(e)}")
        logger.error(traceback.format_exc())
