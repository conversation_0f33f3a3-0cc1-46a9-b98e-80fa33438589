# routes/admin_routes.py
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_required
from core_modules.nas_connector import get_ftp_connector, cached_directory_exists, create_nas_connector
from core_modules.config import Config, PATH_SETTINGS, ITEMS_PER_PAGE
from image_utils.image_processor import get_images_from_folder
from image_utils.batch_manager import get_batch_manager
from databases.db_connector import get_db_connection
from auth.decorators import admin_required
from auth.user_auth import UserAuth
from image_utils.database import db as image_db
from urllib.parse import unquote, quote
from Modules.google_drive import (
    save_drive_config, get_auth_url, is_drive_configured, get_google_drive_service,
    reset_google_drive_config, initialize_admin_drive_connection
)
import json, os, sys, logging, time,traceback

# Add parent directory to path and initialize
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
user_auth = UserAuth()
admin_routes = Blueprint('admin_routes', __name__)
logger = logging.getLogger('admin_routes')

# Fetch Data route for admin dashboard
@admin_routes.route('/fetch-data')
@login_required
@admin_required
def fetch_data_route():
    """Redirect to the data sources page or return JSON source list when requested"""
    # Log session data for debugging
    logger.info(f"Session data in fetch_data_route: {session}")
    logger.info(f"User role: {session.get('role')}")

    # If the client accepts JSON, return the data sources list
    if 'application/json' in request.headers.get('Accept', ''):
        sources = [
            {'key': 'telegram', 'name': 'Telegram', 'icon': 'telegram', 'status': 'connected'},
            {'key': 'twitter', 'name': 'Twitter', 'icon': 'twitter', 'status': 'comingSoon'},
            {'key': 'youtube', 'name': 'YouTube', 'icon': 'youtube', 'status': 'comingSoon'},
            {'key': 'linkedin', 'name': 'LinkedIn', 'icon': 'linkedin', 'status': 'comingSoon'},
            {'key': 'google_drive', 'name': 'Google Drive', 'icon': 'google-drive', 'status': 'comingSoon'},
            {'key': 'instagram', 'name': 'Instagram', 'icon': 'instagram', 'status': 'comingSoon'},
        ]
        return json_response(True, None, sources=sources)

    # Otherwise render the HTML data sources page
    return render_template('admin/data_fetching/data_sources.html', debug_session=session)

# Admin database flush route
@admin_routes.route('/admin/flush-db', methods=['GET', 'POST'])
@login_required
@admin_required
def admin_flush_db():
    """Flush the image-related tables (admin only)"""
    if request.method == 'POST':
        try:
            # Connect to the database
            from databases.db_connector import get_db_connection
            conn = get_db_connection(Config.DATABASE_PATH)
            cursor = conn.cursor()

            # Begin transaction
            conn.execute("BEGIN TRANSACTION")

            # Define tables to clear
            tables_to_clear = [
                "image_processed_manual",
                "image_processed_verification",
                "audit_history",
                "Supervision",
                "sqlite_sequence",  # This is a special table that holds the auto-increment values for all tables
                "dataset",
                "admin_instructions"
            ]

            total_deleted = 0
            # Clear each table
            for table in tables_to_clear:
                cursor.execute(f"DELETE FROM {table}")
                total_deleted += cursor.rowcount

            # Reset indexes in admin settings
            cursor.execute("""
            UPDATE image_admin_settings
            SET value = '"0"', updated_at = CURRENT_TIMESTAMP
            WHERE key = 'current_index';
            """)

            cursor.execute("""
            UPDATE image_admin_settings
            SET value = '"0"', updated_at = CURRENT_TIMESTAMP
            WHERE key = 'verification_current_index';
            """)

            # Commit the changes
            conn.commit()
            conn.close()

            # Flush the Redis cache
            try:
                from cache import flush_cache
                if flush_cache():
                    logger.info("Redis cache database flushed successfully")
                    flash(f'Database tables and Redis cache flushed successfully. {total_deleted} records have been deleted.', 'success')
                else:
                    logger.warning("Failed to flush Redis cache database")
                    flash(f'Database tables flushed successfully, but Redis cache flush failed. {total_deleted} records have been deleted.', 'warning')
            except Exception as cache_error:
                logger.error(f"Error flushing Redis cache: {str(cache_error)}")
                flash(f'Database tables flushed successfully, but Redis cache flush failed: {str(cache_error)}. {total_deleted} records have been deleted.', 'warning')
        except Exception as e:
            logger.error(f"Error flushing database tables: {str(e)}")
            flash(f'Error flushing database tables: {str(e)}', 'danger')
            if 'conn' in locals() and conn:
                conn.rollback()

        return redirect(url_for('admin_routes.manage_users'))

    # GET request - show confirmation page
    return render_template('admin/manage_users/flush_db_confirm.html')

#changes by Abhit Pandey
# NAS Connection Management Route
@admin_routes.route('/admin/connect-nas', methods=['POST'])
@login_required
@admin_required
def connect_nas():
    """Handle NAS connection request from admin dashboard"""
    try:
        # Get connection parameters from form
        nas_type = request.form.get('nas_type')
        nas_url = request.form.get('nas_url')
        nas_username = request.form.get('nas_username')
        nas_password = request.form.get('nas_password')
        redirect_after = request.form.get('redirect_after', 'false') == 'true'

        # Validate inputs
        if not all([nas_type, nas_url, nas_username, nas_password]):
            return json_response(False, 'All fields are required')

        # Normalize URL for FTP
        if not nas_url.startswith('ftp://'):
            nas_url = f"ftp://{nas_url}"

        # Test connection with provided credentials
        try:
            test_connector = create_nas_connector( nas_url, nas_username, nas_password)
            if not test_connector.authenticate():
                return json_response(False, 'Authentication failed with provided credentials')

            # Try listing root directory to verify connection
            test_result = test_connector.list_directory('/')
            if test_result is None:
                return json_response(False, 'Connection succeeded but unable to list directories')

            # Connection successful, store credentials in session for temporary use
            session['custom_nas_settings'] = {
                'type': nas_type,
                'base_url': nas_url,
                'admin_username': nas_username,
                'admin_password': nas_password
            }

            # Update Config class with these settings
            Config.update_nas_settings(session['custom_nas_settings'])

            if redirect_after:
                return json_response(True, 'Successfully connected to NAS',
                                   redirect=True,
                                   redirect_url=url_for('admin_routes.dashboard', nas_connected='true'))

            return json_response(True, 'Successfully connected to NAS',
                               directories_found=len(test_result) if isinstance(test_result, list) else 'Unknown')

        except Exception as e:
            logger.error(f"Error testing NAS connection: {str(e)}")
            return json_response(False, f"Connection error: {str(e)}")

    except Exception as e:
        logger.error(f"Error in NAS connection route: {str(e)}")
        return json_response(False, f"Server error: {str(e)}")

@admin_routes.route('/admin/disconnect-nas')
@login_required
@admin_required
def disconnect_nas():
    """Disconnect from custom NAS by removing session credentials"""
    try:
        if 'custom_nas_settings' in session:
            session.pop('custom_nas_settings')
        Config.update_nas_settings(None)
        return json_response(True, 'Disconnected from NAS')
    except Exception as e:
        logger.error(f"Error disconnecting from NAS: {str(e)}")
        return json_response(False, f"Error disconnecting: {str(e)}")

@admin_routes.route('/admin/check-nas-connection')
@login_required
@admin_required
def check_nas_connection():
    """Check if NAS connection is working"""
    try:
        connected = is_nas_connected()
        using_custom = 'custom_nas_settings' in session if connected else False

        if connected:
            return json_response(True, 'NAS connection is active', connected=True, using_custom=using_custom)
        return json_response(True, 'NAS connection is not active', connected=False)
    except Exception as e:
        logger.error(f"Error checking NAS connection: {str(e)}")
        return json_response(False, f"Error checking connection: {str(e)}")

# NAS Browser API endpoints
@admin_routes.route('/admin/browse-nas-directory')
@login_required
@admin_required
def browse_nas_directory():
    """Browse a directory on the NAS and return its contents"""
    current_path = request.args.get('path', '/')

    try:
        connector = get_ftp_connector()
        if not connector:
            return json_response(False, 'Not connected to NAS. Please connect first.')

        # List all items in the directory
        items = connector.list_directory(current_path)

        if not items:
            return json_response(True, 'Directory is empty', path=current_path, items=[], count=0)

        # Handle different response formats from connectors
        if isinstance(items, dict) and 'data' in items and 'files' in items['data']:
            items_list = items['data']['files']
        elif isinstance(items, list):
            items_list = items
        else:
            items_list = []
            logger.warning(f"Unexpected format received from list_directory for {current_path}: {items}")

        # Get the parent directory for navigation
        parent_path = os.path.dirname(current_path) if current_path != '/' else '/'

        return json_response(True, None, path=current_path, parent_path=parent_path,
                           items=items_list, count=len(items_list))

    except Exception as e:
        logger.error(f"Error browsing NAS directory {current_path}: {str(e)}")
        return json_response(False, f"Error browsing directory: {str(e)}")

@admin_routes.route('/admin/select-manual-folder', methods=['POST'])
@login_required
@admin_required
def select_manual_folder():
    """Select a folder for manual annotation mode"""
    folder_path = request.form.get('folder_path')

    if not folder_path:
        return json_response(False, 'No folder path provided')

    try:
        # Validate the folder exists
        connector = get_ftp_connector()
        if not connector:
            return json_response(False, 'Not connected to NAS. Please connect first.')

        # Check if folder exists
        items = connector.list_directory(folder_path)
        if items is None:
            return json_response(False, f'Folder not found: {folder_path}')

        # Update the database
        if hasattr(image_db, 'set_manual_folder'):
            success = image_db.set_manual_folder(folder_path)
            if not success:
                return json_response(False, 'Failed to set manual annotation folder in database')

            # Invalidate the OCR directory cache to ensure stats are updated
            invalidate_cache('stats', 'ocr_directory')

        # Set annotation mode to manual
        image_db.set_annotation_mode('manual')

        # Create batches using the batch manager
        batch_manager = get_batch_manager()
        success, message, batch_count = batch_manager.create_batches_from_folder(folder_path, mode="manual")

        if not success:
            return json_response(False, f'Folder set but failed to create batches: {message}')

        return json_response(True, f'Manual annotation folder set to: {folder_path}. {message}',
                           folder_path=folder_path,
                           folder_name=os.path.basename(folder_path),
                           batch_count=batch_count)

    except Exception as e:
        logger.error(f"Error setting manual annotation folder: {str(e)}")
        return json_response(False, f'Error: {str(e)}')

@admin_routes.route('/admin/select-verification-folders', methods=['POST'])
@login_required
@admin_required
def select_verification_folders():
    """Select folders for verification mode via browser"""
    image_folder = request.form.get('image_folder')
    label_file = request.form.get('label_file')

    if not image_folder or not label_file:
        return json_response(False, 'Both image folder and label file must be provided')

    # Normalize the paths
    image_folder = image_folder.replace('\\', '/').replace('//', '/')
    if not image_folder.startswith('/'):
        image_folder = '/' + image_folder

    label_file = label_file.replace('\\', '/').replace('//', '/')
    if not label_file.startswith('/'):
        label_file = '/' + label_file

    logger.info(f"Setting verification folders - Image: {image_folder}, Label: {label_file}")

    # Get FTP connector (now the only option)
    content = None
    file_exists = False

    # Get the FTP connector
    ftp_connector = get_ftp_connector()
    if not ftp_connector:
        return json_response(False, 'Failed to connect to FTP storage')

    try:
        # Check image folder
        items = ftp_connector.list_directory(image_folder)
        if not items:
            return json_response(False, f'Image folder not found or empty: {image_folder}')

        # Check label file
        try:
            content = ftp_connector.get_file_content(label_file)
            if content:
                file_exists = True
                logger.info("Successfully validated files via FTP")
            else:
                return json_response(False, f'Label file not found or empty: {label_file}')
        except Exception as e:
            logger.error(f"FTP file read failed: {str(e)}")
            return json_response(False, f'Label file not found or not readable: {label_file}. Error: {str(e)}')
    except Exception as e:
        logger.error(f"FTP validation failed: {str(e)}")
        return json_response(False, f'Error validating files: {str(e)}')

    if not file_exists or not content:
        return json_response(False, f'Label file not found or empty: {label_file}')

    # 3. Validate JSON content with multiple encoding attempts
    try:
        if isinstance(content, bytes):
            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin1']
            decoded = None
            last_error = None

            for encoding in encodings:
                try:
                    decoded = content.decode(encoding)
                    logger.info(f"Successfully decoded file using {encoding} encoding")
                    break
                except UnicodeDecodeError as e:
                    last_error = e
                    continue

            if decoded is None:
                error_msg = f"Failed to decode with any encoding: {str(last_error)}"
                logger.error(error_msg)
                return json_response(False, error_msg)

            content = decoded

        # Check if content is empty
        if not content.strip():
            return json_response(False, f'Label file is empty: {label_file}')

        # Parse JSON content
        try:
            parsed_json = json.loads(content)
        except json.JSONDecodeError as e:
            error_msg = f'Invalid JSON format: {str(e)}'
            logger.error(f"JSON parse error for {label_file}: {str(e)}")
            return json_response(False, error_msg)

        # Validate JSON structure (should be a dictionary mapping filenames to labels)
        if not isinstance(parsed_json, dict):
            error_msg = "JSON file must contain a dictionary mapping filenames to labels"
            logger.error(f"Invalid JSON structure in {label_file}: {error_msg}")
            return json_response(False, error_msg)

        # Check if the dictionary is empty
        if not parsed_json:
            return json_response(False, 'JSON file contains an empty dictionary')

        # Log successful validation
        logger.info(f"Successfully validated JSON file: {label_file} via FTP ({len(parsed_json)} entries)")

    except Exception as e:
        logger.error(f"Error validating JSON file {label_file}: {str(e)}")
        return json_response(False, f'Error validating JSON file: {str(e)}')

    # Set the folders in the database
    success = image_db.set_verification_folders(image_folder, label_file)
    if not success:
        return json_response(False, 'Failed to set verification folders in database')

    # Invalidate the OCR directory cache to ensure stats are updated
    invalidate_cache('stats', 'ocr_directory')

    # Set annotation mode
    image_db.set_annotation_mode('verification')

    # Create batches using the batch manager
    batch_manager = get_batch_manager()
    success, message, batch_count = batch_manager.create_batches_from_folder(image_folder, mode="verification", label_file=label_file)

    if not success:
        return json_response(False, f'Folders set but failed to create batches: {message}')

    return json_response(True, f'Verification folders set successfully. {message}',
                       image_folder=image_folder,
                       image_folder_name=os.path.basename(image_folder),
                       label_file=label_file,
                       label_file_name=os.path.basename(label_file),
                       batch_count=batch_count)

# Get NAS Folders
@admin_routes.route('/admin/get-nas-folders')
@login_required
@admin_required
def get_nas_folders():
    """Get folders from NAS for the connected session"""
    try:
        connector = get_ftp_connector()
        if not connector:
            return json_response(False, 'Not connected to NAS. Please connect first.')

        # List directories
        try:
            root_path = '/'
            items = connector.list_directory(root_path)

            if not items:
                return json_response(True, 'No folders found in root directory', folders=[], count=0)

            # Filter for directories only
            if isinstance(items, list):
                folders = [item for item in items if item.get('type') == 'directory']
            elif isinstance(items, dict) and 'data' in items and 'files' in items['data']:
                folders = [item for item in items['data']['files'] if item.get('type') == 'directory']
            else:
                folders = []

            return json_response(True, None, folders=folders, count=len(folders))
        except Exception as e:
            logger.error(f"Error getting folders from NAS: {str(e)}")
            return json_response(False, f"Error retrieving folders: {str(e)}")

    except Exception as e:
        logger.error(f"Error in get NAS folders route: {str(e)}")
        return json_response(False, f"Server error: {str(e)}")

# Browse NAS for Auditor Image Folder
@admin_routes.route('/admin/set-auditor-image-folder', methods=['POST'])
@login_required
@admin_required
def set_auditor_image_folder():
    """Set the auditor image folder path"""
    try:
        folder_path = request.form.get('folder_path')
        if not folder_path:
            return json_response(False, 'No folder path provided')

        # Normalize path
        folder_path = folder_path.replace('\\', '/').replace('//', '/')
        if not folder_path.startswith('/'):
            folder_path = '/' + folder_path

        # Validate the folder exists
        connector = get_ftp_connector()
        if not connector:
            return json_response(False, 'Not connected to NAS. Please connect first.')

        # Check if folder exists
        items = connector.list_directory(folder_path)
        if items is None:
            return json_response(False, f'Folder not found: {folder_path}')

        # Save to database for persistence
        try:
            success = image_db._set_admin_setting('auditor_image_folder', folder_path)
            if not success:
                return json_response(False, 'Failed to save auditor image folder to database')

            logger.info(f"Auditor image folder set to: {folder_path}")

            # Invalidate cache
            invalidate_cache('stats', 'ocr_directory')

        except Exception as e:
            logger.error(f"Error saving auditor image folder to database: {str(e)}")
            return json_response(False, f'Error saving to database: {str(e)}')

        return json_response(True, f'Auditor image folder set to: {folder_path}',
                           folder_path=folder_path, folder_name=os.path.basename(folder_path))

    except Exception as e:
        logger.error(f"Error setting auditor image folder: {str(e)}")
        return json_response(False, f'Error: {str(e)}')

# Utility functions
def is_nas_connected():
    """Check if NAS connection is active"""
    try:
        connector = get_ftp_connector()
        if connector and connector.authenticate():
            test_result = connector.list_directory('/')
            if test_result is not None:
                return True
        return False
    except Exception as e:
        logger.error(f"Error in is_nas_connected: {str(e)}")
        return False

def json_response(success, message=None, **kwargs):
    """Create a standardized JSON response"""
    response = {'success': success}
    if message:
        response['message'] = message
    response.update(kwargs)
    return jsonify(response)

def invalidate_cache(cache_type, key=None):
    """Invalidate cache of specified type"""
    try:
        if cache_type == 'stats':
            from cache.admin_cache import invalidate_stats_cache
            invalidate_stats_cache(key or 'ocr_directory')
            logger.info(f"Invalidated {key or 'ocr_directory'} stats cache")
        elif cache_type == 'directory':
            from cache.admin_cache import invalidate_directory_cache
            invalidate_directory_cache(key)
            logger.info(f"Invalidated directory cache for {key}")
        elif cache_type == 'user':
            from cache.admin_cache import invalidate_user_cache
            invalidate_user_cache(key)  # key can be username or None for all users
            logger.info(f"Invalidated user cache for {key or 'all users'}")
        return True
    except Exception as e:
        logger.warning(f"Error invalidating {cache_type} cache: {str(e)}")
        return False

# Dashboard route for admins (Simplified)
@admin_routes.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Main admin dashboard view."""
    try:
        # Load auditor image folder from database
        auditor_image_folder = image_db.get_auditor_image_folder()
        # Update Config with the value from database
        # Get configuration information for template
        config_data = {
            'AUDITOR_IMAGE_FOLDER': auditor_image_folder,
            'NAS_CONNECTED': is_nas_connected()
        }

        # Check if we have drive auth flags in session from previous redirect
        if 'drive_auth_success' in session:
            # Add URL parameter for the JS in admin/dashboard.html to detect
            session.pop('drive_auth_success')
            return redirect(url_for('admin_routes.dashboard', drive_auth_success='true'))

        if 'drive_auth_error' in session:
            error_msg = session.pop('drive_auth_error_msg', 'Authentication failed')
            session.pop('drive_auth_error')
            return redirect(url_for('admin_routes.dashboard', drive_auth_error='true', error_msg=error_msg))

        # Pass the URL query parameters to the template (for OAuth callbacks)
        auth_params = {
            'drive_auth_success': request.args.get('drive_auth_success'),
            'drive_auth_error': request.args.get('drive_auth_error'),
            'error_msg': request.args.get('error_msg'),
            'nas_connected': request.args.get('nas_connected')
        }

        return render_template('admin/dashboard.html', config=config_data, **auth_params)
    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}")
        logger.exception("Detailed error:")
        flash('An error occurred while loading the dashboard.', 'danger')
        # Use a simpler config to render the page in case of error
        config_data = {
            'AUDITOR_IMAGE_FOLDER': auditor_image_folder,
            'NAS_CONNECTED': False  # Safe default
        }
        return render_template('admin/dashboard.html', config=config_data)
#change_ends

# OCR Directory Management Route
@admin_routes.route('/ocr-directory')
@login_required
@admin_required
def admin_ocr_directory():
    """Manages OCR directory settings (manual/verification modes)."""
    try:
        # Check if refresh parameter is set
        refresh = request.args.get('refresh', '0') == '1'

        # If refresh is requested, invalidate the cache
        if refresh:
            if invalidate_cache('stats', 'ocr_directory'):
                flash("Directory information refreshed", "success")

        # Try to get cached OCR directory data if not refreshing
        try:
            from cache.admin_cache import get_cached_stats, cache_stats

            cached_data = None if refresh else get_cached_stats('ocr_directory')
            if cached_data is not None:
                logger.debug("Using cached OCR directory data")
                # If the client accepts JSON, return the OCR directory data as JSON
                if 'application/json' in request.headers.get('Accept', ''):
                    return json_response(True, None, **cached_data)
                return render_template('admin/ocr_directory/admin_ocr_directory.html', **cached_data)
        except Exception as cache_error:
            logger.warning(f"Error using cache for OCR directory: {str(cache_error)}")

        # If cache fails or no cached data, get from database and filesystem
        # Get stats about images
        stats = {
            'image_folders': [],
            'processing_folders': [],
            'verification_image_folders': [],
            'verification_label_folders': [],
            'json_files_count': 0  # Add counter for JSON files
        }

        connector = get_ftp_connector()
        if not connector:
            # Return JSON when requested by AJAX
            if 'application/json' in request.headers.get('Accept', ''):
                return json_response(True, None, stats=stats, user=session, annotation_mode='manual')
            flash('Failed to connect to FTP', 'danger')
            return render_template('admin/ocr_directory/admin_ocr_directory.html', stats=stats, user=session, annotation_mode='manual')

        # Get base directory from PATH_SETTINGS
        base_dir = PATH_SETTINGS.get('base_directory', '')

        # If base_dir is empty, don't try to access it
        if not base_dir:
            logger.info("No base directory set in PATH_SETTINGS")
            # Don't show an error, just continue without trying to access NAS directories
        else:
            logger.info(f"Using base directory from PATH_SETTINGS: {base_dir}")

        # Get annotation mode from database
        annotation_mode = image_db.get_annotation_mode() if hasattr(image_db, 'get_annotation_mode') else 'manual'

        # Get manual folder data
        manual_folder = image_db.get_manual_folder() if hasattr(image_db, 'get_manual_folder') else ''
        manual_folder_name = os.path.basename(manual_folder) if manual_folder else ''

        # Get verification folder data
        verification_image_folder = image_db.get_verification_image_folder() if hasattr(image_db, 'get_verification_image_folder') else ''
        verification_label_folder = image_db.get_verification_label_folder() if hasattr(image_db, 'get_verification_label_folder') else ''
        verification_image_folder_name = os.path.basename(verification_image_folder) if verification_image_folder else ''
        verification_label_folder_name = os.path.basename(verification_label_folder) if verification_label_folder else ''

        # Get progress information from the batch manager
        batch_manager = get_batch_manager()

        # Get progress for manual mode
        manual_progress = batch_manager.get_processing_progress(mode="manual")
        total_images = manual_progress['total_images']
        processed_manual_count = manual_progress['processed_images']
        current_index = manual_progress.get('current_index', 0)

        # Get progress for verification mode
        verification_progress = batch_manager.get_processing_progress(mode="verification")
        verification_total_images = verification_progress['total_images']
        processed_verification_count = verification_progress['processed_images']
        verification_current_index = verification_progress.get('current_index', 0)

        # Log the progress information
        logger.info(f"Manual progress: {processed_manual_count}/{total_images} images ({manual_progress['progress_percentage']:.1f}%)")
        logger.info(f"Verification progress: {processed_verification_count}/{verification_total_images} images ({verification_progress['progress_percentage']:.1f}%)")

        # Get admin instructions
        manual_instructions = image_db.get_admin_instructions('manual') if hasattr(image_db, 'get_admin_instructions') else Config.MANUAL_INSTRUCTIONS
        verification_instructions = image_db.get_admin_instructions('verification') if hasattr(image_db, 'get_admin_instructions') else Config.VERIFICATION_INSTRUCTIONS

        # Get JSON files from the OCR LABELS directory
        json_files = []
        # Only try to get JSON files if we have a base directory
        if base_dir:
            # Construct label path based on base directory (if available)
            # Don't hardcode a path that might not exist on the user's system
            label_path = os.path.join(base_dir, "OCR LABELS") if base_dir else ""

            if label_path:
                try:
                    items = connector.list_directory(label_path)
                    if items:
                        # Handle potential API format differences
                        if isinstance(items, dict) and 'data' in items and 'files' in items['data']:
                            file_list = items['data']['files']
                        elif isinstance(items, list):
                            file_list = items
                        else:
                            file_list = []
                            logger.warning(f"Unexpected format received from list_directory for {label_path}: {items}")

                        json_files = [{'path': item.get('path', os.path.join(label_path, item.get('name', ''))),
                                    'name': item.get('name', '')}
                                    for item in file_list if item.get('type') == 'file' and item.get('name', '').endswith('.json')]
                        stats['json_files_count'] = len(json_files)
                        logger.info(f"Found {len(json_files)} JSON files in {label_path}")
                except Exception as e:
                    logger.error(f"Error listing JSON files in {label_path}: {str(e)}")
        else:
                    logger.info("No label path available, skipping JSON file listing")

        # Get currently selected label file if any
        selected_label_file = verification_label_folder

        # List folders in the base directory
        try:
            if base_dir and cached_directory_exists(connector, base_dir):
                folders_list = connector.list_directory(base_dir)

                # Handle the case where we get a different response format
                if isinstance(folders_list, dict) and 'data' in folders_list:
                     folder_data = folders_list.get('data', {}).get('files', [])
                elif isinstance(folders_list, list):
                    folder_data = folders_list
                else:
                    folder_data = []
                    logger.warning(f"Unexpected format received from list_directory for {base_dir}: {folders_list}")

                # Filter for directories only
                processing_folders_raw = [item for item in folder_data if
                                     item.get('type', '').lower() in ['directory', 'dir', 'folder']]

                # Get stats for each processing folder
                for folder in processing_folders_raw:
                    folder_path = folder.get('path', os.path.join(base_dir, folder.get('name', '')))
                    folder_name = folder.get('name', os.path.basename(folder_path))
                    if not folder_path or not folder_name:
                        logger.warning(f"Skipping folder item with missing path or name: {folder}")
                        continue

                    # Count images in the folder
                    try:
                        images = get_images_from_folder(folder_path, page=1, items_per_page=50000, recursive=True)[0]
                        image_count = len(images)
                    except Exception as img_count_e:
                        logger.error(f"Error counting images in folder {folder_path}: {img_count_e}")
                        image_count = 0 # Default to 0 if count fails


                    # Add to processing folders
                    stats['processing_folders'].append({
                        'path': folder_path,
                        'name': folder_name,
                        'image_count': image_count,
                    })
            elif not base_dir:
                logger.info("No base directory set, skipping folder listing")
            else:
                 flash(f"Base directory '{base_dir}' not found or inaccessible.", 'warning')
        except Exception as e:
            logger.error(f"Error listing processing folders in {base_dir}: {str(e)}")
            logger.exception("Detailed error:")
            flash("Error loading image folders from NAS.", 'danger')

        # Prepare context for the template
        context = {
            'stats': stats,
            'user': session,
            'annotation_mode': annotation_mode,
            'manual_folder': manual_folder,
            'manual_folder_name': manual_folder_name,
            'current_index': current_index,
            'total_images': total_images,
            'processed_manual_count': processed_manual_count,
            'verification_image_folder': verification_image_folder,
            'verification_label_folder': verification_label_folder,
            'verification_image_folder_name': verification_image_folder_name,
            'verification_label_folder_name': verification_label_folder_name,
            'verification_current_index': verification_current_index,
            'verification_total_images': verification_total_images,
            'processed_verification_count': processed_verification_count,
            'json_files': json_files,
            'selected_label_file': selected_label_file,
            'manual_instructions': manual_instructions,
            'verification_instructions': verification_instructions
        }

        # Cache the context for future requests
        try:
            from cache.admin_cache import cache_stats
            cache_stats('ocr_directory', context)
            logger.debug("Cached OCR directory data")
        except Exception as cache_error:
            logger.warning(f"Error caching OCR directory data: {str(cache_error)}")

        # If the client accepts JSON, return the OCR directory data as JSON
        if 'application/json' in request.headers.get('Accept', ''):
            return json_response(True, None, **context)
        return render_template('admin/ocr_directory/admin_ocr_directory.html', **context)

    except Exception as e:
        logger.error(f"Error in admin_ocr_directory route: {str(e)}")
        logger.exception("Detailed error:")
        flash('An error occurred while loading the OCR directory settings.', 'danger')
        # Attempt to render with default context on error
        return render_template('admin/ocr_directory/admin_ocr_directory.html', stats={}, user=session, annotation_mode='manual')

# Browser route for admins
@admin_routes.route('/browser')
@login_required
@admin_required
def browser():
    """File browser route - only accessible to admins"""
    folder = unquote(request.args.get('folder', PATH_SETTINGS['image_folders'][0] if PATH_SETTINGS['image_folders'] else ''))
    logger.info(f"Browser accessing folder: '{folder}'")

    page = int(request.args.get('page', 1))
    if annotate_count := request.form.get('annotate'):
        return redirect(url_for('annotator_routes.annotate_route', folder=quote(folder), count=int(annotate_count)))

    refresh = request.args.get('refresh', '0') == '1'
    cache_key = f"{folder}:{page}"
    
    if refresh and invalidate_cache('directory', cache_key):
        flash(f"Directory listing refreshed for {folder}", "success")

    try:
        from cache.admin_cache import get_cached_directory_listing, cache_directory_listing
        cached_data = None if refresh else get_cached_directory_listing(cache_key)

        if cached_data:
            logger.debug(f"Using cached directory listing for {folder} (page {page})")
            images, total, total_pages = [cached_data.get(k) for k in ('images', 'total', 'total_pages')]
        else:
            images, total = get_images_from_folder(folder, page=page) if folder else ([], 0)
            total_pages = (total + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE if folder else 0
            
            cache_directory_listing(cache_key, {
                'images': images,
                'total': total,
                'total_pages': total_pages
            })
            logger.debug(f"Cached directory listing for {folder} (page {page})")
    except Exception as cache_error:
        logger.warning(f"Error using cache for directory listing: {str(cache_error)}")
        images, total = get_images_from_folder(folder, page=page)
        total_pages = (total + ITEMS_PER_PAGE - 1) // ITEMS_PER_PAGE

    parent_folder = os.path.dirname(folder)
    if parent_folder == folder:
        parent_folder = None

    return render_template('admin/ocr_directory/browser.html',
                          images=images,
                          folder=folder,
                          available_folders=PATH_SETTINGS['image_folders'],
                          page=page,
                          total_pages=total_pages,
                          total_images=total,
                          parent_folder=parent_folder,
                          user=session)

# Manage users route
@admin_routes.route('/manage-users')
@login_required
@admin_required
def manage_users():
    """Manage users route - only accessible to admins"""
    refresh = request.args.get('refresh', '0') == '1'

    if refresh and invalidate_cache('user'):
        flash("User list refreshed", "success")

    try:
        from cache.admin_cache import get_cached_user_list, cache_user_list
        if not refresh and (cached_users := get_cached_user_list()):
            logger.debug("Using cached user list")
            return render_template('admin/manage_users/manage_users.html', users=cached_users, user=session)
    except Exception as cache_error:
        logger.warning(f"Error using cache for user list: {str(cache_error)}")
        logger.error(f"Cache error details: {traceback.format_exc()}")

    users = user_auth.get_all_users()
    try:
        cache_user_list(users)
        logger.debug("Cached user list")
    except Exception as cache_error:
        logger.warning(f"Error caching user list: {str(cache_error)}")
        logger.error(f"Cache error details: {traceback.format_exc()}")

    return render_template('admin/manage_users/manage_users.html', users=users, user=session)

# Edit user route
@admin_routes.route('/edit-user/<username>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(username):
    """Edit user route - only accessible to admins"""
    try:
        # Try cached user details first, fallback to database
        from cache.admin_cache import get_cached_user_details, cache_user_details
        user = get_cached_user_details(username) or user_auth.get_user(username)
        
        if user and not get_cached_user_details(username):
            cache_user_details(username, user)
            logger.debug(f"Cached user details for {username}")
    except Exception as e:
        logger.error(f"Cache error for user {username}: {str(e)}")
        user = user_auth.get_user(username)

    if not user:
        flash(f'User {username} not found', 'danger')
        return redirect(url_for('admin_routes.manage_users'))

    if request.method == 'POST':
        update_params = {
            'full_name': request.form.get('full_name'),
            'role': request.form.get('role'),
            'email': request.form.get('email')
        }
        
        if all([update_params['full_name'], update_params['role']]):
            if update_params['role'] == 'annotator':
                annotation_mode = request.form.get('annotation_mode')
                if annotation_mode:
                    update_params['annotation_mode'] = annotation_mode
                    logger.info(f"Setting annotation_mode to {annotation_mode} for user {username}")

            success, message = user_auth.update_user(username, **update_params)
            if success:
                invalidate_cache('user', username)
                flash(f'User {username} updated successfully', 'success')
                return redirect(url_for('admin_routes.manage_users'))
            flash(f'Error updating user: {message}', 'danger')

    return render_template('admin/manage_users/edit_user.html', user=user, session_user=session)

# Suspend user route
@admin_routes.route('/suspend-user/<username>', methods=['POST'])
@login_required
@admin_required
def suspend_user(username):
    """Suspend or unsuspend a user"""
    action = request.form.get('action', 'suspend')

    if action == 'suspend':
        success = user_auth.suspend_user(username)
        if success:
            invalidate_cache('user', username)
            flash(f'User {username} has been suspended', 'success')
        else:
            flash(f'Failed to suspend user {username}', 'danger')
    elif action == 'unsuspend':
        success = user_auth.unsuspend_user(username)
        if success:
            invalidate_cache('user', username)
            flash(f'User {username} has been unsuspended', 'success')
        else:
            flash(f'Failed to unsuspend user {username}', 'danger')

    return redirect(url_for('admin_routes.manage_users'))

# Admin Synthetic Upload route
@admin_routes.route('/synthetic-upload')
@login_required
@admin_required
def admin_synthetic_upload():
    """Synthetic data upload page for admin dashboard"""
    try:
        return render_template('admin/admin_synthetic_upload.html')
    except Exception as e:
        logger.error(f"Error rendering admin_synthetic_upload.html: {str(e)}")
        flash("Error loading synthetic data upload page", "danger")
        return redirect(url_for('admin_routes.dashboard'))

# Google Drive Configuration Route
@admin_routes.route('/admin/configure-google-drive', methods=['POST'])
@login_required
@admin_required
def configure_google_drive():
    """Configure Google Drive connection"""
    try:
        # Get folder_id from form
        folder_id = request.form.get('folder_id')
        client_id = request.form.get('client_id')
        client_secret = request.form.get('client_secret')
 
        # Log the received parameters for debugging
        logger.info(f"Configuring Google Drive with folder_id: {folder_id or 'default'}")
       
        # Make sure client credentials are provided
        if not client_id or not client_secret:
            return jsonify({
                'success': False,
                'message': 'Client ID and Client Secret are required'
            })
 
        # Save the full configuration using the helper function
        client_secret_filename = save_drive_config(client_id, client_secret, folder_id)
       
        if not client_secret_filename:
            return jsonify({
                'success': False,
                'message': 'Failed to save Google Drive configuration'
            })
 
        # Mark the session as belonging to an admin
        session['role'] = 'admin'
 
        # Set specific callback URL for this session (helps with the OAuth flow)
        callback_url = url_for('admin_routes.google_drive_callback', _external=True)
        session['drive_callback_url'] = callback_url
        logger.info(f"Set callback URL: {callback_url}")
 
        # Try to initialize the connection with existing credentials
        is_initialized = initialize_admin_drive_connection()
        if is_initialized:
            # We already have valid credentials, no need for auth URL
            return jsonify({
                'success': True,
                'message': 'Google Drive connection successful with existing credentials',
                'connected': True
            })
 
        # Get authentication URL for OAuth flow
        auth_url = get_auth_url()
        if not auth_url:
            return jsonify({
                'success': False,
                'message': 'Failed to generate authentication URL'
            })
 
        logger.info(f"Generated auth URL starting with: {auth_url[:50]}...")
 
        return jsonify({
            'success': True,
            'message': 'Google Drive configured successfully. Please complete authentication.',
            'auth_url': auth_url,
            'connected': False
        })
 
    except Exception as e:
        logger.error(f"Error configuring Google Drive: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        })

@admin_routes.route('/admin/check-google-drive-connection')
@login_required
@admin_required
def check_google_drive_connection():
    """Check if Google Drive is connected and authenticated"""
    try:
        connected = is_drive_configured()
        logger.info(f"Google Drive connection check: {connected}")

        if connected:
            logger.info("Drive is configured - connection successful")
            try:
                if service := get_google_drive_service():
                    files = service.files().list(pageSize=1).execute()
                    logger.info(f"Test API call successful, found {len(files.get('files', []))} files")
            except Exception as api_error:
                logger.error(f"Connection test failed on API call: {str(api_error)}")
                connected = False

        return jsonify({'success': True, 'connected': connected})
    except Exception as e:
        logger.error(f"Error checking Google Drive connection: {str(e)}")
        return jsonify({'success': False, 'connected': False, 'error': str(e)})

# Google Drive OAuth Callback
@admin_routes.route('/admin/google-drive-callback')
@login_required
@admin_required
def google_drive_callback():
    """Handle OAuth callback after authentication"""
    auth_code = request.args.get('code')
    state = request.args.get('state')
    error = request.args.get('error')
    
    logger.info(f"OAuth callback received. Code present: {bool(auth_code)}, State: {state}, Error: {error}")
    
    if error or not auth_code:
        error_msg = error or "No authorization code received"
        logger.error(f"OAuth error: {error_msg}")
        flash(f'Authentication failed: {error_msg}', 'danger')
        return render_template('admin/data_fetching/google_auth_error.html', error=error_msg)

    try:
        session['temp_auth_code'] = auth_code
        service = get_google_drive_service()
        if not service:
            raise Exception("Failed to initialize Google Drive service")
            
        flash('Google Drive authentication completed successfully!', 'success')
        return render_template('admin/data_fetching/google_auth_success.html')
    except Exception as e:
        logger.error(f"Error in Google Drive callback: {str(e)}")
        flash(f'Error authenticating with Google Drive: {str(e)}', 'danger')
        return render_template('admin/data_fetching/google_auth_error.html', error=str(e))

def handle_oauth_callback(auth_code, state, error, scope):
    """Handle OAuth callback from Google"""
    logger.info(f"OAuth handler called: code={bool(auth_code)}, state={state}, error={error}, scope={bool(scope)}")
    
    if not auth_code or not (state or scope):
        logger.info("Not a valid OAuth callback")
        return False, None
        
    logger.info("Processing OAuth callback")
    try:
        session['temp_auth_code'] = auth_code
        service = get_google_drive_service()
        if not service:
            raise Exception("Failed to initialize Drive service")
            
        # Test API call
        files = service.files().list(pageSize=1).execute()
        logger.info(f"API call successful! Found {len(files.get('files', []))} files")
        session['drive_auth_success'] = True
        
        if 'username' in session:
            flash('Google Drive connected successfully!', 'success')
            return True, redirect(url_for('admin_routes.dashboard', drive_auth_success='true'))
        return True, render_template('admin/data_fetching/google_auth_success.html')
            
    except Exception as e:
        error_msg = f"API call failed: {str(e)}" if 'files' in locals() else str(e)
        logger.error(f"Error completing Google auth: {error_msg}")
        session['drive_auth_error'] = True
        session['drive_auth_error_msg'] = error_msg
        
        if 'username' in session:
            return True, redirect(url_for('admin_routes.dashboard', drive_auth_error='true', error_msg=error_msg))
        return True, render_template('admin/data_fetching/google_auth_error.html', error=error_msg)

# Add a route to reset Google Drive configuration
@admin_routes.route('/admin/reset-google-drive', methods=['POST'])
@login_required
@admin_required
def reset_google_drive():
    """Reset all Google Drive configuration and credentials"""
    try:
        success = reset_google_drive_config()

        if success:
            flash('Google Drive configuration has been reset. You will need to reconfigure it.', 'success')
            return jsonify({
                'success': True,
                'message': 'Google Drive configuration has been reset'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to reset Google Drive configuration'
            })
    except Exception as e:
        logger.error(f"Error resetting Google Drive configuration: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        })

@admin_routes.route('/edit-instructions', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_instructions():
    """Display & save dataset-specific instructions and supervision instructions."""
    conn = get_db_connection()
    cursor = conn.cursor()

    if request.method == 'POST':
        mode = request.form.get('mode', '').strip()

        if mode == 'supervision':
            instructions = request.form.get('supervision_instructions', '').strip()
            if not instructions:
                flash('Instructions are required.', 'danger')
                return redirect(url_for('admin_routes.edit_instructions', mode='supervision'))

            try:
                cursor.execute("REPLACE INTO admin_instructions (mode, instructions, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)", (mode, instructions))
                conn.commit()
                flash('Supervision mode instructions saved.', 'success')
                logger.info("Saved supervision instructions to DB.")
            except Exception as e:
                logger.error(f"DB error saving supervision instructions: {e}")
                flash('Failed to save instructions.', 'danger')

            return redirect(url_for('admin_routes.edit_instructions', mode='supervision'))

        dataset_id = request.form.get('dataset_id', '').strip()
        instructions = request.form.get('dataset_instructions', '').strip()
        if not mode or not dataset_id or not instructions:
            flash('Mode, dataset, and instructions are all required.', 'danger')
            return redirect(url_for('admin_routes.edit_instructions', mode=mode))

        try:
            cursor.execute("""
                UPDATE dataset SET instructions = ?, mode = ? WHERE id = ?
            """, (instructions, mode, dataset_id))
            
            cursor.execute("""
                SELECT instructions FROM admin_instructions 
                WHERE mode = ? AND (instructions IS NULL OR instructions = '')
            """, (mode,))
            row = cursor.fetchone()
            if row is not None:
                cursor.execute("""
                    UPDATE admin_instructions 
                    SET instructions = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE mode = ?
                """, (instructions, mode))
                logger.info(f"Updated empty instructions for mode {mode} in admin_instructions")

            conn.commit()

            row = cursor.execute("SELECT dataset_name FROM dataset WHERE id = ?", (dataset_id,)).fetchone()
            name = row['dataset_name'] if row else 'Unknown'
            flash(f'Instructions for dataset "{name}" saved.', 'success')
            logger.info(f"Saved instructions for dataset {name} (ID: {dataset_id})")
        except Exception as e:
            logger.error(f"DB error saving dataset instructions for dataset {dataset_id}: {e}")
            flash('Failed to save dataset instructions.', 'danger')
        
        return redirect(url_for('admin_routes.edit_instructions', mode=mode, dataset=dataset_id))

    # GET request
    selected_mode = request.args.get('mode', '')
    selected_dataset_id = request.args.get('dataset', '')
    logger.info(f"GET edit_instructions - mode: '{selected_mode}', dataset_id: '{selected_dataset_id}'")

    context = {
        'selected_mode': selected_mode,
        'selected_dataset_id': selected_dataset_id,
        'dataset_instructions': '',
        'supervision_instructions': '',
        'manual_datasets': [],
        'verification_datasets': [],
        'datasets': []
    }

    row = cursor.execute("SELECT instructions FROM admin_instructions WHERE mode = 'supervision'").fetchone()
    context['supervision_instructions'] = row['instructions'] if row else ''

    if selected_mode in ['manual', 'verification']:
        query = f"SELECT id, dataset_name FROM dataset WHERE mode = ? ORDER BY dataset_name"
        datasets = cursor.execute(query, (selected_mode,)).fetchall()
        context[f'{selected_mode}_datasets'] = datasets
        context['datasets'] = datasets
        logger.info(f"Found {len(datasets)} {selected_mode} datasets")

        if selected_dataset_id:
            try:
                row = cursor.execute(
                    "SELECT id, dataset_name, instructions FROM dataset WHERE id = ?",
                    (selected_dataset_id,)
                ).fetchone()
                if row:
                    logger.info(f"Found dataset: {row['dataset_name']} (ID: {row['id']})")
                    context['dataset_instructions'] = row['instructions'] or ''
                else:
                    logger.warning(f"Dataset with ID {selected_dataset_id} not found in database")
            except Exception as e:
                logger.error(f"Error fetching dataset with ID {selected_dataset_id}: {e}")

    conn.close()
    logger.info(f"Context prepared - selected_mode: '{context['selected_mode']}', " +
                f"selected_dataset_id: '{context['selected_dataset_id']}', " +
                f"datasets count: {len(context.get('datasets', []))}")

    # JSON API for edit instructions
    if 'application/json' in request.headers.get('Accept', ''):
        return json_response(True, None,
            supervision_instructions=context['supervision_instructions'],
            manual_datasets=[{'id': d['id'], 'name': d['dataset_name']} for d in context.get('manual_datasets', context.get('datasets', []))],
            verification_datasets=[{'id': d['id'], 'name': d['dataset_name']} for d in context.get('verification_datasets', context.get('datasets', []))],
            dataset_instructions=context['dataset_instructions']
        )

    return render_template('admin/ocr_directory/edit_instructions.html', **context)

@admin_routes.route('/admin/auditor-tracking')
@login_required
@admin_required
def auditor_tracking():
    """
    Admin dashboard for tracking auditor batch completion statistics.
    Shows how many batches have been completed by each auditor out of the total assigned.
    Also displays datasets grouped by their merge status.
    """
    try:
        dataset_name = request.args.get('dataset')
        datasets = get_audit_datasets()

        valid_ids = [d['id'] for d in datasets]
        if not dataset_name or dataset_name not in valid_ids:
            dataset_name = datasets[0]['id'] if datasets else None

        # Fetch statistics for selected dataset
        batch_manager = get_batch_manager()
        if dataset_name:
            stats = batch_manager.get_auditor_batch_statistics(dataset_name)
            total_completed = stats.get('total_completed', 0)
            total_batches = stats.get('total_batches', 0)
        else:
            total_completed = 0
            total_batches = 0
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""SELECT id, dataset_name, status FROM dataset ORDER BY dataset_name""")
        
        db_datasets = cursor.fetchall()
        conn.close()
        
        merged_datasets = []
        pending_datasets = []
        
        for dataset in db_datasets:
            dataset_info = {
                'id': dataset['id'],
                'name': dataset['dataset_name'],
            }
            
            if dataset['status'].lower() == 'merged':
                merged_datasets.append(dataset_info)
            else:
                pending_datasets.append(dataset_info)

        return json_response(True,
                             datasets=datasets,
                             selected_dataset=dataset_name,
                             total_completed=total_completed,
                             total_batches=total_batches,
                             merged_datasets=merged_datasets,
                             pending_datasets=pending_datasets)
    except Exception as e:
        logger.error(f"Error in auditor tracking route: {str(e)}")
        return json_response(False, str(e))
    
def get_audit_datasets():
    """Get the list of available datasets for auditing"""
    try:
        datasets = []

        # Check if NAS connector is available to get datasets from file system
        connector = get_ftp_connector()
        if not connector:
            logger.error("Failed to get NAS connector. Cannot fetch datasets from NAS.")
            return []

        # Get the audit folder path based on verification mode
        AUDIT_FOLDER = '/Data/DATP Datasets/VERIFIED DATASET'

        # Check if directory exists
        from auditor import check_directory_exists 
        nas_exists = check_directory_exists(
            connector,
            AUDIT_FOLDER,
            log_prefix="Datasets: "
        )

        # Get datasets from NAS filesystem if directory exists
        if nas_exists:
            nas_items = connector.list_directory(AUDIT_FOLDER)
            if nas_items:
                logger.info(f"Found {len(nas_items)} items in {AUDIT_FOLDER}")
                for item in nas_items:
                    if isinstance(item, dict) and item.get('type') == 'directory':
                        dataset_name = item.get('name')
                        # Add this dataset to our list
                        datasets.append({'id': dataset_name, 'name': dataset_name})
                        logger.info(f"Added dataset from NAS: {dataset_name}")
            else:
                logger.warning(f"NAS directory {AUDIT_FOLDER} is empty")
        else:
            logger.error(f"NAS directory {AUDIT_FOLDER} does not exist")

        logger.info(f"Returning total of {len(datasets)} datasets")
        return datasets
    except Exception as e:
        logger.error(f"Error getting audit datasets: {str(e)}")
        return []

@admin_routes.route('/admin/merge-dataset-json', methods=['POST'])
@login_required
@admin_required
def merge_dataset_json():
    """
    Merge all JSON files in a dataset folder and save the result to the output folder.
    """
    try:
        dataset_name = request.form.get('dataset_name')
        storage_destination = request.form.get('storage_destination', 'current')
        
        if not dataset_name:
            return json_response(False, 'No dataset name provided')
        
        paths = {
            'input': f'/Data/DATP Datasets/VERIFIED DATASET/{dataset_name}',
            'output': f'/Data/DATP Datasets/Final_Dataset',
            'output_file': f'{dataset_name}.json'
        }
        
        connector = get_ftp_connector()
        if not connector or not connector.directory_exists(paths['input']):
            error_msg = 'Failed to connect to NAS' if not connector else f'Dataset directory not found: {paths["input"]}'
            return json_response(False, error_msg)
        
        if storage_destination == 'current' and not connector.directory_exists(paths['output']):
            logger.info(f"Creating output directory: {paths['output']}")
            if not connector.create_directory(paths['output']):
                return json_response(False, f'Failed to create output directory: {paths["output"]}')
        
        items = connector.list_directory(paths['input'])
        json_files = [item for item in items if isinstance(item, dict) and 
                     item.get('type') == 'file' and 
                     item.get('name', '').lower().endswith('.json')] if items else []

        if not json_files:
            return json_response(False, f'No JSON files found in dataset directory: {paths["input"]}')
        
        merged_data = {}
        file_count = 0
        
        for json_file in json_files:
            file_path = f"{paths['input']}/{json_file.get('name')}"
            logger.info(f"Processing file: {file_path}")
            try:
                content = connector.get_file_content(file_path)
                if not content:
                    logger.warning(f"Could not read file content: {file_path}")
                    continue
                
                for encoding in ['utf-8', 'utf-8-sig', 'latin1']:
                    try:
                        file_data = json.loads(content.decode(encoding))
                        if isinstance(file_data, dict):
                            merged_data.update(file_data)
                            file_count += 1
                            break
                    except (UnicodeDecodeError, json.JSONDecodeError):
                        continue
                else:
                    logger.warning(f"Could not decode file with any encoding: {file_path}")
            except Exception as e:
                logger.error(f"Error processing file {file_path}: {str(e)}")
        
        if file_count == 0:
            return json_response(False, 'No valid JSON files could be processed')
        
        json_content = json.dumps(merged_data, indent=2, ensure_ascii=False).encode('utf-8')
        upload_success = False
        drive_link = None
        file_id = None
        
        if storage_destination == 'current':
            full_output_path = f"{paths['output']}/{paths['output_file']}"
            upload_success = connector.save_file(full_output_path, json_content)
            if upload_success:
                logger.info(f"Successfully merged {file_count} JSON files into {full_output_path}")
            else:
                return json_response(False, f'Failed to save merged file to {full_output_path}')
                
        elif storage_destination == 'google-drive':
            try:
                from Modules.google_drive import get_google_drive_service, is_drive_configured
                from googleapiclient.http import MediaIoBaseUpload
                from io import BytesIO
                
                if not is_drive_configured():
                    return json_response(False, 'Google Drive is not configured. Please configure it in the Admin Dashboard.')
                
                drive_service = get_google_drive_service()
                if not drive_service:
                    return json_response(False, 'Failed to connect to Google Drive')
                
                def ensure_folder_exists(name, parent_id=None):
                    query = f"name='{name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
                    query += f" and '{parent_id}' in parents" if parent_id else ""
                    logger.info(f"Searching for folder with query: {query}")
                    
                    result = drive_service.files().list(q=query, spaces='drive', fields='files(id, name)').execute()
                    if result.get('files'):
                        folder_id = result['files'][0]['id']
                        logger.info(f"Found existing folder: {name} with ID: {folder_id}")
                        return folder_id
                    
                    folder_metadata = {
                        'name': name,
                        'mimeType': 'application/vnd.google-apps.folder',
                        'parents': [parent_id] if parent_id else None
                    }
                    try:
                        folder = drive_service.files().create(body=folder_metadata, fields='id').execute()
                        folder_id = folder.get('id')
                        logger.info(f"Created new folder: {name} with ID: {folder_id}")
                        return folder_id
                    except Exception as e:
                        logger.error(f"Error creating folder '{name}': {str(e)}")
                        return None
                
                # Create folder structure
                root_id = ensure_folder_exists("DATP Datasets")
                final_id = root_id and ensure_folder_exists("Final_Dataset", root_id)
                
                file_metadata = {
                    'name': paths['output_file'],
                    'mimeType': 'application/json',
                    'parents': [final_id] if final_id else None
                }
                
                media = MediaIoBaseUpload(BytesIO(json_content), mimetype='application/json', resumable=True)
                drive_file = drive_service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id,webViewLink'
                ).execute()
                
                file_id = drive_file.get('id')
                drive_link = drive_file.get('webViewLink')
                upload_success = bool(file_id)
                
                logger.info(f"Successfully uploaded {paths['output_file']} to Google Drive with ID: {file_id}")
                
            except Exception as drive_error:
                logger.error(f"Error uploading to Google Drive: {str(drive_error)}")
                return json_response(False, f'Error uploading to Google Drive: {str(drive_error)}')
        else:
            return json_response(False, f'Storage destination "{storage_destination}" not yet implemented')
        
        if upload_success:
            try:
                with get_db_connection() as conn:
                    conn.execute("UPDATE dataset SET status = 'merged' WHERE dataset_name = ?", (dataset_name,))
                    conn.commit()
                logger.info(f"Updated dataset {dataset_name} status to 'merged'")
            except Exception as db_error:
                logger.error(f"Error updating dataset status: {str(db_error)}")
        
        success_message = f'Successfully merged {file_count} JSON files'
        if storage_destination == 'google-drive':
            success_message += ' and uploaded to Google Drive'
            if final_id:
                success_message += ' folder "DATP Datasets/Final_Dataset"'
        
        response = {
            'success': True,
            'message': success_message,
            'file_count': file_count,
            'output_file': paths['output_file'],
            'total_entries': len(merged_data)
        }
        
        if storage_destination == 'google-drive':
            response.update({'drive_link': drive_link, 'drive_file_id': file_id})
            
        return json_response(**response)
        
    except Exception as e:
        logger.error(f"Error merging dataset JSON: {str(e)}")
        return json_response(False, f'Error: {str(e)}')

# API route to select a dataset for annotation or verification
@admin_routes.route('/admin/select-dataset', methods=['POST'])
@login_required
@admin_required
def select_dataset():
    """Select a dataset for annotation or verification"""
    conn = None
    try:
        dataset_id, mode = request.form.get('dataset_id'), request.form.get('mode')
        if not dataset_id or not mode or mode not in ['manual', 'verification']:
            return json_response(False, 'Invalid parameters: Dataset ID and valid mode (manual/verification) required')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get dataset details and validate
        cursor.execute("SELECT dataset_name, label_folder, mode, total_batch, annotated_batch, instructions FROM dataset WHERE id = ?", (dataset_id,))
        dataset = cursor.fetchone()
        if not dataset or dataset['mode'] != mode:
            conn.close()
            return json_response(False, f'Dataset not found or mode mismatch (requested: {mode}, actual: {dataset["mode"] if dataset else "N/A"})')
        
        dataset_name, label_folder, dataset_instructions = dataset['dataset_name'], dataset['label_folder'], dataset['instructions'] or ''
        
        # Get folder path from batches
        cursor.execute(f"SELECT folder_name FROM image_processed_{mode} WHERE batch_name LIKE ? LIMIT 1", 
                      (f"{mode}_{dataset_name}_%",))
        folder_result = cursor.fetchone()
        if not folder_result:
            conn.close()
            return json_response(False, f'No batches found for dataset {dataset_name} in {mode} mode')
        
        folder_path = folder_result['folder_name']
        
        # Update or insert instructions
        cursor.execute("SELECT id FROM admin_instructions WHERE mode = ?", (mode,))
        if cursor.fetchone():
            cursor.execute("UPDATE admin_instructions SET instructions = ?, updated_at = CURRENT_TIMESTAMP WHERE mode = ?",
                         (dataset_instructions, mode))
            logger.info(f"Updated {mode} instructions in admin_instructions table")
        else:
            cursor.execute("INSERT INTO admin_instructions (mode, instructions, created_at, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)",
                         (mode, dataset_instructions))
            logger.info(f"Inserted new {mode} instructions in admin_instructions table")
        
        conn.commit()
        
        # Update admin settings with retry logic
        max_retries, success = 3, False
        for retry_count in range(max_retries):
            try:
                if mode == 'manual':
                    if hasattr(image_db, 'set_manual_folder'):
                        if not image_db.set_manual_folder(folder_path):
                            if retry_count == max_retries - 1:
                                conn.close()
                                return json_response(False, f'Failed to set manual folder after {max_retries} attempts')
                            continue
                    image_db.set_annotation_mode('manual')
                else:
                    if not image_db.set_verification_folders(folder_path, label_folder):
                        if retry_count == max_retries - 1:
                            conn.close()
                            return json_response(False, f'Failed to set verification folders after {max_retries} attempts')
                        continue
                    image_db.set_annotation_mode('verification')
                success = True
                break
            except Exception as retry_error:
                logger.error(f"Attempt {retry_count + 1}: Error setting database values: {str(retry_error)}")
                if retry_count < max_retries - 1:
                    time.sleep(0.5 * (2 ** retry_count))
                else:
                    raise
        
        if conn:
            try:
                conn.commit()
            except Exception as commit_error:
                logger.error(f"Error committing changes: {str(commit_error)}")
            finally:
                conn.close()
        
        invalidate_cache('stats', 'ocr_directory')
        
        return json_response(True, f'Successfully selected {mode} dataset: {dataset_name}',
                           dataset_name=dataset_name,
                           folder_path=folder_path,
                           label_folder=label_folder if mode == 'verification' else None,
                           total_batches=dataset['total_batch'],
                           completed_batches=dataset['annotated_batch'],
                           instructions_updated=True)
        
    except Exception as e:
        logger.error(f"Error selecting dataset: {str(e)}")
        if conn:
            try:
                conn.close()
            except Exception:
                pass
        return json_response(False, f'Error: {str(e)}')

# API route to get available datasets
@admin_routes.route('/admin/get-datasets', methods=['GET'])
@login_required
@admin_required
def get_datasets():
    """Get available datasets for a specific mode"""
    try:
        mode = request.args.get('mode')
        if not mode or mode not in ['manual', 'verification']:
            return json_response(False, 'Invalid or missing mode parameter')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT d.id, d.dataset_name, d.label_folder, d.total_batch, d.annotated_batch,
                   (SELECT folder_name FROM image_processed_%s WHERE batch_name LIKE ? LIMIT 1) as folder_path
            FROM dataset d WHERE mode = ? ORDER BY dataset_name
        """ % mode, (f"{mode}_%_", mode))
        
        datasets = [{
            'id': row['id'],
            'name': row['dataset_name'],
            'label_file': row['label_folder'] if mode == 'verification' else None,
            'total_batches': row['total_batch'],
            'completed_batches': row['annotated_batch'],
            'folder_path': row['folder_path'] or "",
            'progress_percentage': round((row['annotated_batch'] / row['total_batch']) * 100 if row['total_batch'] > 0 else 0, 1)
        } for row in cursor.fetchall()]
        
        conn.close()
        
        current_folder = image_db._get_admin_setting('manual_folder' if mode == 'manual' else 'verification_image_folder', '')
        current_dataset_id = next((d['id'] for d in datasets if d['name'] == os.path.basename(current_folder)), None) if current_folder else None
        
        logger.info(f"Found {len(datasets)} datasets for {mode} mode")
        return json_response(True, f'Found {len(datasets)} datasets for {mode} mode',
                           datasets=datasets,
                           current_dataset_id=current_dataset_id,
                           count=len(datasets))
        
    except Exception as e:
        logger.error(f"Error getting datasets: {str(e)}")
        return json_response(False, f'Error: {str(e)}')