#!/usr/bin/env python3
"""
Test script to verify the backend migration from HTML templates to API-only endpoints.
This script tests the key API endpoints to ensure they return JSON responses.
"""

import requests
import json
import sys

# Configuration
BASE_URL = 'http://localhost:8080'
NEXTJS_URL = 'http://localhost:3000'

def test_endpoint(url, method='GET', data=None, expected_status=200, description=""):
    """Test an API endpoint and verify it returns JSON"""
    try:
        print(f"\n🧪 Testing: {description}")
        print(f"   URL: {method} {url}")
        
        if method == 'GET':
            response = requests.get(url, timeout=10)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=10)
        else:
            print(f"   ❌ Unsupported method: {method}")
            return False
            
        print(f"   Status: {response.status_code}")
        
        # Check if response is JSON
        try:
            json_data = response.json()
            print(f"   ✅ Returns valid JSON")
            if 'success' in json_data:
                print(f"   Success: {json_data['success']}")
            return True
        except json.JSONDecodeError:
            print(f"   ❌ Response is not valid JSON")
            print(f"   Content-Type: {response.headers.get('content-type', 'unknown')}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Request failed: {str(e)}")
        return False

def test_nextjs_frontend():
    """Test if Next.js frontend is accessible"""
    try:
        print(f"\n🌐 Testing Next.js Frontend")
        print(f"   URL: GET {NEXTJS_URL}")
        
        response = requests.get(NEXTJS_URL, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Next.js frontend is accessible")
            return True
        else:
            print(f"   ❌ Next.js frontend returned status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Next.js frontend not accessible: {str(e)}")
        return False

def main():
    """Run all migration tests"""
    print("🚀 Backend Migration Test Suite")
    print("=" * 50)
    
    tests = [
        # Test session endpoint
        (f"{BASE_URL}/api/session", "GET", None, 200, "Session API endpoint"),
        
        # Test admin dashboard (requires auth, but should return JSON error)
        (f"{BASE_URL}/api/admin/dashboard", "GET", None, 401, "Admin dashboard API endpoint"),
        
        # Test admin OCR directory (requires auth, but should return JSON error)
        (f"{BASE_URL}/api/admin/ocr-directory", "GET", None, 401, "Admin OCR directory API endpoint"),
        
        # Test auth login endpoint with invalid credentials
        (f"{BASE_URL}/api/auth/login", "POST", {"username": "test", "password": "invalid"}, 401, "Auth login API endpoint"),
        
        # Test root redirect
        (f"{BASE_URL}/", "GET", None, 302, "Root redirect to Next.js"),
    ]
    
    passed = 0
    total = len(tests)
    
    for url, method, data, expected_status, description in tests:
        if test_endpoint(url, method, data, expected_status, description):
            passed += 1
    
    # Test Next.js frontend
    if test_nextjs_frontend():
        passed += 1
    total += 1
    
    print(f"\n📊 Test Results")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Migration appears successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
