from flask import Blueprint, request, jsonify, render_template
import logging
from ocr_utils.ocr_extractor import process_image_data, extract_text_from_image_data, cleanup_old_image_metadata, chat_about_image_data
from ocr_utils.ocr_extractor import extract_text_from_image, chat_about_image, get_image_data
from ocr_utils.ocr_extractor import API_URL, API_HEADERS
import requests
import base64
import io

extractor_routes = Blueprint('extractor_routes', __name__)

logger = logging.getLogger('extractor_routes')

@extractor_routes.route('/extractor-mode')
def extractor_mode():
    """
    Normal mode route for OCR extraction from uploaded images
    """
    # Clean up old image metadata
    cleanup_old_image_metadata()
    
    # Render the normal mode template
    return render_template('note_ocr/extractor.html')

@extractor_routes.route('/api/extractor-mode/upload', methods=['POST'])
def upload_image():
    """
    API route to handle image uploads for normal mode
    """
    try:
        # Check if image was provided
        if 'image' not in request.files:
            logger.error("No image file in request")
            return jsonify({'success': False, 'error': 'No image file provided'}), 400
        
        image_file = request.files['image']
        
        # Check if filename is valid
        if image_file.filename == '':
            logger.error("Empty filename in image upload")
            return jsonify({'success': False, 'error': 'No filename provided'}), 400
        
        # Process the uploaded image without saving
        success, result = process_image_data(image_file)
        
        if success:
            return jsonify({
                'success': True,
                'image_id': result['image_id'],
                'filename': result['filename']
            })
        else:
            logger.error(f"Error processing uploaded image: {result}")
            return jsonify({'success': False, 'error': result}), 500
    except Exception as e:
        logger.error(f"Error in upload_image route: {str(e)}")
        logger.exception("Detailed error:")
        return jsonify({'success': False, 'error': str(e)}), 500

@extractor_routes.route('/api/extractor-mode/extract-ocr', methods=['POST'])
def extract_ocr():
    """
    API route to extract text from an uploaded image using OCR
    """
    try:
        # Get request data
        data = request.get_json() if request.is_json else {}
        image_id = data.get('image_id')
        
        # Check if image data is included in request
        if 'image_data' in data:
            # Extract directly from image data
            image_data = data.get('image_data')
            if image_data and isinstance(image_data, str) and image_data.startswith('data:image'):
                # Handle base64 image data
                header, encoded = image_data.split(",", 1)
                image_bytes = base64.b64decode(encoded)
                
                # Extract text from the image data
                success, result = extract_text_from_image_data(image_bytes)
            else:
                return jsonify({'success': False, 'error': 'Invalid image data format'}), 400
        elif 'image' in request.files:
            # Check if image is provided in request files
            image_file = request.files['image']
            success, result = extract_text_from_image_data(image_file, image_file.filename)
        elif image_id:
            # Extract text using image_id
            logger.info(f"Extracting text for image with ID: {image_id}")
            success, result = extract_text_from_image(image_id)
        else:
            logger.error("No image data or image_id provided for OCR extraction")
            return jsonify({'success': False, 'error': 'No image data or image_id provided'}), 400
        
        if success:
            return jsonify({
                'success': True,
                'text': result
            })
        else:
            logger.error(f"Error extracting text: {result}")
            return jsonify({'success': False, 'error': result}), 500
    except Exception as e:
        logger.error(f"Error in extract_ocr route: {str(e)}")
        logger.exception("Detailed error:")
        return jsonify({'success': False, 'error': str(e)}), 500

@extractor_routes.route('/api/extractor-mode/extract-ocr-custom', methods=['POST'])
def extract_ocr_custom():
    """
    API route to extract text from an uploaded image using a custom prompt
    """
    try:
        # Get request data
        data = request.get_json() if request.is_json else {}
        image_id = data.get('image_id')
        custom_prompt = data.get('prompt')
        
        if not custom_prompt:
            logger.error("No prompt provided for custom OCR extraction")
            return jsonify({'success': False, 'error': 'No custom prompt provided'}), 400
        
        # Check if image data is included in request
        if 'image_data' in data:
            # Use image data directly
            image_data = data.get('image_data')
            if image_data and isinstance(image_data, str) and image_data.startswith('data:image'):
                # Handle base64 image data
                header, encoded = image_data.split(",", 1)
                image_bytes = base64.b64decode(encoded)
                
                # Prepare files for the API request
                files = {
                    'file': ('image.jpg', io.BytesIO(image_bytes)),
                    'question': (None, custom_prompt)
                }
            else:
                return jsonify({'success': False, 'error': 'Invalid image data format'}), 400
        elif 'image' in request.files:
            # Check if image is provided in request files
            image_file = request.files['image']
            
            # Prepare files for the API request
            files = {
                'file': (image_file.filename, image_file),
                'question': (None, custom_prompt)
            }
        elif image_id:
            # Get image data from in-memory storage
            logger.info(f"Processing custom prompt for image with ID: {image_id}")
            success, image_info = get_image_data(image_id)
            
            if not success:
                logger.error(f"Failed to get image data: {image_info}")
                return jsonify({'success': False, 'error': image_info}), 404
                
            # Prepare files for the API request
            files = {
                'file': (image_info['filename'], io.BytesIO(image_info['image_data'])),
                'question': (None, custom_prompt)
            }
        else:
            logger.error("No image data or image_id provided for custom OCR extraction")
            return jsonify({'success': False, 'error': 'No image data or image_id provided'}), 400
                
        # Make the API request
        logger.info(f"Making API request to {API_URL} with custom prompt")
        response = requests.post(API_URL, headers=API_HEADERS, files=files)
        
        # Check for errors
        if response.status_code == 200:
            extracted_text = response.json()["response"]
            
            # Get OCR extractor for post-processing
            from ocr_utils.ocr_extractor import get_ocr_extractor
            ocr_extractor = get_ocr_extractor()
            
            # Post-process the extracted text
            processed_text = ocr_extractor._post_process_extracted_text(extracted_text)
            
            logger.info(f"Successfully extracted text with custom prompt via API")
            return jsonify({
                'success': True,
                'text': processed_text,
                'method': 'api'
            })
        else:
            error_msg = f"API Error {response.status_code}: {response.text}"
            logger.error(error_msg)
            return jsonify({'success': False, 'error': error_msg}), 500
            
    except Exception as e:
        logger.error(f"Error in extract_ocr_custom route: {str(e)}")
        logger.exception("Detailed error:")
        return jsonify({'success': False, 'error': str(e)}), 500

@extractor_routes.route('/api/extractor-mode/chat', methods=['POST'])
def image_chat():
    """
    API route to process a chat query about an uploaded image
    """
    try:
        # Get request data
        data = request.get_json() if request.is_json else {}
        image_id = data.get('image_id')
        query = data.get('query') or request.form.get('query')
        chat_history = data.get('chat_history')
        
        if not query:
            logger.error("No query provided for chat")
            return jsonify({'success': False, 'error': 'No chat query provided'}), 400
        
        # Validate chat history if provided
        if chat_history is not None and not isinstance(chat_history, list):
            logger.warning(f"Invalid chat history format: {type(chat_history)}, using None")
            chat_history = None
        
        # Log the request
        logger.info(f"Chat request with query: {query}")
        
        # Check if image data is included in request
        if 'image_data' in data:
            # Use image data directly
            image_data = data.get('image_data')
            if image_data and isinstance(image_data, str) and image_data.startswith('data:image'):
                # Handle base64 image data
                header, encoded = image_data.split(",", 1)
                image_bytes = base64.b64decode(encoded)
                
                # Process the chat query about the image
                success, result = chat_about_image_data(image_bytes, query, chat_history=chat_history)
            else:
                return jsonify({'success': False, 'error': 'Invalid image data format'}), 400
        elif 'image' in request.files:
            # Check if image is provided in request files
            image_file = request.files['image']
            success, result = chat_about_image_data(image_file, query, image_file.filename, chat_history)
        elif image_id:
            # Process chat query using image_id
            logger.info(f"Processing chat query for image with ID: {image_id}")
            success, result = chat_about_image(image_id, query, chat_history)
        else:
            logger.error("No image data or image_id provided for chat")
            return jsonify({'success': False, 'error': 'No image data or image_id provided'}), 400
        
        if success:
            # Log successful response length for debugging
            result_length = len(result) if result else 0
            logger.info(f"Chat response generated successfully, length: {result_length} chars")
            
            return jsonify({
                'success': True,
                'response': result
            })
        else:
            error_msg = str(result) if result else "Unknown error"
            logger.error(f"Error processing chat query: {error_msg}")
            
            # If we tried with history and failed, suggest trying without
            if chat_history:
                error_msg += " (tried with chat history)"
            
            return jsonify({'success': False, 'error': error_msg}), 500
    except Exception as e:
        logger.error(f"Error in image_chat route: {str(e)}")
        logger.exception("Detailed error:")
        return jsonify({'success': False, 'error': f"Server error: {str(e)}"}), 500