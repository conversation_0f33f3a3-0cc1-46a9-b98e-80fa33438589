import axios from 'axios';
import { ApiResponse } from '@/types';

// Create axios instance with base configuration
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Enable cookies for session management
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token');
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// API helper functions
export const api = {
  // Authentication
  auth: {
    login: (credentials: { username: string; password: string }) =>
      apiClient.post<ApiResponse>('/api/auth/login', credentials),

    logout: () =>
      apiClient.post<ApiResponse>('/api/auth/logout'),

    register: (userData: {
      username: string;
      password: string;
      confirm_password: string;
      role?: string;
      full_name?: string;
      email?: string;
      annotation_mode?: string;
    }) =>
      apiClient.post<ApiResponse>('/api/auth/register', userData),

    session: () =>
      apiClient.get<ApiResponse>('/api/session'),

    changePassword: (data: { current_password: string; new_password: string }) =>
      apiClient.post<ApiResponse>('/api/auth/change-password', data),
  },

  // User management
  users: {
    list: (params?: { page?: number; per_page?: number; role?: string }) =>
      apiClient.get<ApiResponse>('/api/users', { params }),

    create: (userData: any) =>
      apiClient.post<ApiResponse>('/api/users', userData),

    update: (id: string, userData: any) =>
      apiClient.put<ApiResponse>(`/api/users/${id}`, userData),

    delete: (id: string) =>
      apiClient.delete<ApiResponse>(`/api/users/${id}`),

    get: (id: string) =>
      apiClient.get<ApiResponse>(`/api/users/${id}`),
  },

  // File management
  files: {
    browse: (path?: string) =>
      apiClient.get<ApiResponse>('/api/files/browse', { params: { path } }),

    upload: (formData: FormData) =>
      apiClient.post<ApiResponse>('/api/files/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),

    delete: (path: string) =>
      apiClient.delete<ApiResponse>('/api/files', { params: { path } }),
  },

  // Annotation tasks
  annotations: {
    list: (params?: { status?: string; assigned_to?: string; page?: number }) =>
      apiClient.get<ApiResponse>('/api/annotator/annotations', { params }),

    get: (id: string) =>
      apiClient.get<ApiResponse>(`/api/annotator/annotations/${id}`),

    create: (data: any) =>
      apiClient.post<ApiResponse>('/api/annotator/annotations', data),

    update: (id: string, data: any) =>
      apiClient.put<ApiResponse>(`/api/annotator/annotations/${id}`, data),

    save: (id: string, annotationData: any) =>
      apiClient.post<ApiResponse>(`/api/annotator/annotations/${id}/save`, annotationData),

    submit: (id: string) =>
      apiClient.post<ApiResponse>(`/api/annotator/annotations/${id}/submit`),
  },

  // Audit tasks
  audits: {
    list: (params?: { status?: string; page?: number }) =>
      apiClient.get<ApiResponse>('/api/auditor/audits', { params }),

    get: (id: string) =>
      apiClient.get<ApiResponse>(`/api/auditor/audits/${id}`),

    approve: (id: string, feedback?: string) =>
      apiClient.post<ApiResponse>(`/api/auditor/audits/${id}/approve`, { feedback }),

    reject: (id: string, feedback: string) =>
      apiClient.post<ApiResponse>(`/api/auditor/audits/${id}/reject`, { feedback }),
  },

  // Admin functions
  admin: {
    dashboard: () =>
      apiClient.get<ApiResponse>('/api/admin/dashboard'),

    ocrDirectory: () =>
      apiClient.get<ApiResponse>('/api/admin/ocr-directory'),

    nasConnect: (config: any) =>
      apiClient.post<ApiResponse>('/api/admin/connect-nas', config),

    nasDisconnect: () =>
      apiClient.post<ApiResponse>('/api/admin/disconnect-nas'),

    nasStatus: () =>
      apiClient.get<ApiResponse>('/api/admin/check-nas-connection'),

    nasBrowse: (path?: string) =>
      apiClient.get<ApiResponse>('/api/admin/browse-nas-directory', { params: { path } }),

    setAuditorFolder: (folderPath: string) =>
      apiClient.post<ApiResponse>('/api/admin/set-auditor-image-folder', { folder_path: folderPath }),

    googleDriveConfig: (config: any) =>
      apiClient.post<ApiResponse>('/api/admin/configure-google-drive', config),

    googleDriveStatus: () =>
      apiClient.get<ApiResponse>('/api/admin/check-google-drive-connection'),

    googleDriveReset: () =>
      apiClient.post<ApiResponse>('/api/admin/reset-google-drive'),
  },

  // Dashboard
  dashboard: {
    stats: () =>
      apiClient.get<ApiResponse>('/api/dashboard/stats'),

    userStats: (userId?: string) =>
      apiClient.get<ApiResponse>('/api/dashboard/user-stats', { params: { user_id: userId } }),
  },
};
