from typing import List
import re
import unicodedata
from string import punctuation
from collections import Counter
import nltk
from nltk.tokenize import word_tokenize
from nltk.util import ngrams
from nltk.corpus import stopwords
from nltk.corpus import words as nltk_words
from routes.synthetic.vector_store import vector_store
import logging
import numpy as np
logger = logging.getLogger(__name__)
nltk.download('punkt_tab')
nltk.download('words')

class KeywordExtractor:
    def __init__(self):
        # Initialize NLTK for English processing
        try:
            nltk.data.find('tokenizers/punkt')
            nltk.data.find('corpora/stopwords')
        except LookupError:
            nltk.download('punkt')
            nltk.download('stopwords')
        
        # Expanded stopwords list with tech and general terms
        self.stop_words = set(stopwords.words('english'))
        # Add common technical stop words and low-value terms that shouldn't be in keywords
        self.stop_words.update([
            'table', 'column', 'row', 'selector', 'simply', 'point', 'tutorial', 'tutorials',
            'entity', 'value', 'values', 'example', 'examples', 'using', 'used', 'use',
            'following', 'following', 'show', 'shown', 'shows', 'see', 'seen', 'looks', 'like',
            'way', 'ways', 'make', 'makes', 'made', 'learn', 'learning', 'learned', 'get', 'gets',
            'got', 'getting', 'set', 'sets', 'setting', 'create', 'creates', 'created', 'creating',
            'let', 'lets', 'letting', 'try', 'tries', 'tried', 'trying', 'note', 'notes', 'noting',
            'please', 'would', 'could', 'should', 'might', 'may', 'can', 'will', 'shall',
            'horizontal', 'vertical', 'record', 'records', 'first', 'second', 'third', 'last', 'next',
            'prev', 'previous', 'above', 'below', 'between', 'inside', 'outside', 'easy', 'simple',
            'data', 'tab', 'tabs', 'field', 'fields', 'text', 'type', 'types', 'name', 'names',
            'date', 'time', 'day', 'month', 'year', 'hour', 'minute', 'second'
        ])
        
        self.max_word_length = 30
        self.max_words_per_keyword = 3
        self.min_words_per_keyword = 1
        self.min_word_length = 3
        # Add English vocabulary for word validation
        self.english_vocab = set(w.lower() for w in nltk_words.words())
        
    def _is_english_only(self, text: str) -> bool:
        """
        Check if the string contains only English characters.
        """
        # Only allow ASCII letters, spaces, and hyphens (for compound words)
        pattern = re.compile(r'^[a-zA-Z\s-]+$')
        return bool(pattern.match(text))
    
    def _clean_keyword(self, keyword: str) -> str:
        """
        Clean keyword by removing symbols and ensuring it's English only.
        Preserves complete words and proper spacing.
        """
        # Remove all punctuation except hyphens
        cleaned = ''.join(c for c in keyword if c.isalpha() or c.isspace() or c == '-')
        # Normalize spaces - replace multiple spaces with single space
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        # Ensure no more than 3 words
        words = cleaned.split()
        if len(words) > 3:
            cleaned = ' '.join(words[:3])
        return cleaned
        
    def process_english_text(self, text: str) -> List[str]:
        """Process English text to extract meaningful keywords and phrases."""
        # Tokenize and clean text
        words = word_tokenize(text.lower())
        words = [word for word in words if word.isalnum()]
        
        # Remove stopwords, short words, numbers, and non-English words
        filtered_words = [w for w in words if w not in self.stop_words 
                         and len(w) >= self.min_word_length
                         and not w.isdigit()
                         and not self._is_mostly_numeric(w)
                         and self._is_english_only(w)]
        
        # Generate n-grams (1-3 words)
        unigrams = [(w,) for w in filtered_words]
        
        # For bigrams, we'll be more lenient - allow some shorter words when part of a bigram
        bigram_words = [w for w in words if w not in self.stop_words 
                       and len(w) >= max(2, self.min_word_length - 1)
                       and not w.isdigit()
                       and not self._is_mostly_numeric(w)
                       and self._is_english_only(w)]
        
        bigrams = list(ngrams(bigram_words, 2))
        trigrams = list(ngrams(words, 3))
        
        # Filter n-grams to remove those containing stopwords, numbers, or non-English words
        filtered_bigrams = []
        for gram in bigrams:
            if not any(word in self.stop_words 
                    or len(word) < max(2, self.min_word_length - 1)
                    or word.isdigit()
                    or self._is_mostly_numeric(word)
                    or not self._is_english_only(word) for word in gram):
                filtered_bigrams.append(gram)
                
        filtered_trigrams = []
        for gram in trigrams:
            if not any(word in self.stop_words 
                    or len(word) < self.min_word_length
                    or word.isdigit()
                    or self._is_mostly_numeric(word)
                    or not self._is_english_only(word) for word in gram):
                filtered_trigrams.append(gram)
        
        # Combine all n-grams
        all_grams = unigrams + filtered_bigrams + filtered_trigrams
        
        # Convert to strings and count frequencies
        keyword_phrases = [' '.join(gram) for gram in all_grams]
        keyword_freq = Counter(keyword_phrases)
        
        # Set minimum frequency threshold based on document size
        min_freq = 2 if len(filtered_words) > 200 else 1
        
        # Filter by minimum frequency
        keyword_freq = {k: v for k, v in keyword_freq.items() if v >= min_freq}
        
        # Sort by frequency and length
        sorted_keywords = sorted(
            keyword_freq.items(),
            key=lambda x: (
                -x[1],                    # Frequency (descending)
                -len(x[0].split()),       # Number of words (descending)
                len(x[0]),                # Total length (ascending)
                x[0]                      # Alphabetically
            )
        )
        
        # Get unique keywords, removing substrings of other keywords
        unique_keywords = []
        keyword_set = set()
        
        for keyword, _ in sorted_keywords:
            # Clean the keyword to remove any symbols
            cleaned_keyword = self._clean_keyword(keyword)
            if not cleaned_keyword or not self._is_english_only(cleaned_keyword):
                continue
                
            # Skip if keyword is a substring of an already selected keyword
            if any(cleaned_keyword in kw for kw in keyword_set):
                continue
                
            # Skip if keyword contains another keyword that already exists
            contains_other = False
            for existing_kw in keyword_set:
                if existing_kw in cleaned_keyword and existing_kw != cleaned_keyword:
                    contains_other = True
                    break
                    
            if not contains_other:
                unique_keywords.append(cleaned_keyword)
                keyword_set.add(cleaned_keyword)
                
            # Limit to reasonable number
            if len(unique_keywords) >= 20:
                break
        
        return unique_keywords
        
    def extract_keywords_with_vector_store(self, text: str, max_keywords: int = 15) -> List[str]:
        """
        Extract keywords using vector store's semantic capabilities.
        This approach uses embedding similarity to identify key conceptual phrases.
        """
        try:
            # Create candidate keywords using traditional NLP first
            candidates = self.process_english_text(text)
            
            # If we don't have enough candidates, try to create more
            if not candidates or len(candidates) < max_keywords * 1.5:
                # Try to extract more candidates by using a more aggressive approach
                # Extract n-grams more aggressively
                import re
                # Split into sentences
                sentences = re.split(r'[.!?]+', text)
                sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
                
                # Extract additional candidates from important sentences
                additional_candidates = []
                
                # Focus on first and last sentences, and sentences with important terms
                important_terms = ["key", "main", "important", "significant", "primary", "critical", 
                                 "essential", "major", "central", "core", "fundamental"]
                
                important_sentences = []
                if sentences:
                    # Add first 2 and last 2 sentences
                    important_sentences.extend(sentences[:min(2, len(sentences))])
                    important_sentences.extend(sentences[-min(2, len(sentences)):])
                    
                    # Add sentences with important terms
                    for sentence in sentences:
                        if any(term in sentence.lower() for term in important_terms):
                            important_sentences.append(sentence)
                
                # Extract noun phrases and other important phrases
                for sentence in important_sentences:
                    # Simple noun phrase extraction (2-3 word sequences)
                    words = sentence.lower().split()
                    for i in range(len(words) - 1):
                        phrase = words[i] + " " + words[i+1]
                        if len(phrase) > 5 and not any(w in self.stop_words for w in phrase.split()):
                            additional_candidates.append(phrase)
                    
                    for i in range(len(words) - 2):
                        phrase = words[i] + " " + words[i+1] + " " + words[i+2]
                        if len(phrase) > 8 and not any(w in self.stop_words for w in phrase.split()):
                            additional_candidates.append(phrase)
                
                # Add the additional candidates
                candidates.extend(additional_candidates)
                
                # Remove duplicates
                candidates = list(set(candidates))
                
                # If still not enough, fall back to traditional method
                if not candidates or len(candidates) < max_keywords:
                    return self.extract_keywords(text, max_keywords)
                
            # Use vector search to find the most representative keywords
            # Create an embedding for the full text
            context_embedding = vector_store.embedding_function([text])[0]
            
            # Get embeddings for all candidates
            candidate_embeddings = vector_store.embedding_function(candidates)
            
            # Segment the text into sentences for determining concept importance
            import re
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
            
            # Calculate importance scores that combine:
            # 1. Semantic similarity to the entire document
            # 2. Appearance in important sentences (beginning/end)
            # 3. Reduced weight for frequency
            importance_scores = []
            
            # Get document embedding
            doc_embedding = context_embedding
            
            for i, candidate in enumerate(candidates):
                # Calculate semantic similarity to document
                candidate_embedding = candidate_embeddings[i]
                semantic_score = self._cosine_similarity(candidate_embedding, doc_embedding)
                
                # Calculate positional importance (terms at beginning/end of document or paragraphs)
                positional_score = 0
                first_sentences = sentences[:min(3, len(sentences))]
                last_sentences = sentences[-min(3, len(sentences)):]
                important_sentences = first_sentences + last_sentences
                
                # Check if candidate appears in important positions
                for sentence in important_sentences:
                    if candidate.lower() in sentence.lower():
                        positional_score += 0.5
                
                # Count occurrences but with diminishing returns (log scale)
                import math
                count = text.lower().count(candidate.lower())
                if count > 0:
                    frequency_score = math.log(count + 1) / 10  # Reduced weight for frequency
                else:
                    frequency_score = 0
                    
                # Check if term is a noun phrase (more likely to be a concept)
                words = candidate.split()
                conceptual_score = 0
                if len(words) > 1:  # Multi-word phrases often represent concepts
                    conceptual_score += 0.3
                
                # Calculate combined score with weights favoring semantic meaning
                combined_score = (
                    semantic_score * 0.5 +     # 50% semantic similarity
                    positional_score * 0.3 +   # 30% position in document
                    frequency_score * 0.1 +    # 10% frequency (reduced weight)
                    conceptual_score * 0.1     # 10% conceptual nature
                )
                
                importance_scores.append((candidate, combined_score))
            
            # Sort by importance score (descending)
            sorted_candidates = sorted(importance_scores, key=lambda x: -x[1])
            
            # Remove duplicates and near-duplicates
            unique_candidates = []
            seen_concepts = set()
            
            for candidate, score in sorted_candidates:
                # Skip if empty
                if not candidate or not candidate.strip():
                    continue
                    
                # Skip if we've seen a similar concept
                normalized = candidate.lower()
                already_seen = False
                
                for seen in seen_concepts:
                    if (seen in normalized or normalized in seen or 
                        self._similarity_score(seen, normalized) > 0.6):
                        already_seen = True
                        break
                
                if not already_seen:
                    unique_candidates.append(candidate)
                    seen_concepts.add(normalized)
                    
                # Stop when we have enough
                if len(unique_candidates) >= max_keywords:
                    break
            
            # Select top candidates
            top_candidates = unique_candidates[:max_keywords]
            
            # Fill with empty strings if we don't have enough
            while len(top_candidates) < max_keywords:
                top_candidates.append("")
                
            return top_candidates
            
        except Exception as e:
            print(f"Error extracting keywords with vector store: {str(e)}")
            # Fall back to traditional method
            return self.extract_keywords(text, max_keywords)
            
    def _cosine_similarity(self, vec1, vec2):
        """Calculate cosine similarity between two vectors"""
        dot_product = np.dot(vec1, vec2)
        norm_1 = np.linalg.norm(vec1)
        norm_2 = np.linalg.norm(vec2)
        return dot_product / (norm_1 * norm_2)

    def extract_keywords(self, text: str, max_keywords: int = 20) -> List[str]:
        """
        Extract keywords from English text.
        """
        try:
            # Normalize text
            text = unicodedata.normalize('NFKC', text)
            
            # Process English text
            keywords = self.process_english_text(text)
            
            # Post-process to remove duplicates and improve quality
            processed_keywords = self._post_process_keywords(keywords)
            
            # Ensure exactly max_keywords
            if len(processed_keywords) > max_keywords:
                # Make sure we include some 2-word keywords if available
                two_word_count = sum(1 for k in processed_keywords[:max_keywords] if len(k.split()) == 2)
                if two_word_count < max_keywords // 4:  # At least 25% should be 2-word keywords
                    # Find more 2-word keywords
                    additional_two_word = [k for k in processed_keywords[max_keywords:] 
                                          if len(k.split()) == 2][:max_keywords // 4 - two_word_count]
                    # Replace some 1-word or 3-word keywords with 2-word keywords
                    processed_keywords = processed_keywords[:max_keywords - len(additional_two_word)] + additional_two_word
                else:
                    processed_keywords = processed_keywords[:max_keywords]
            
            # Fill with empty strings if needed
            while len(processed_keywords) < max_keywords:
                processed_keywords.append("")
                
            return processed_keywords
                
        except Exception as e:
            print(f"Error extracting keywords: {str(e)}")
            return [""] * max_keywords
    
    def _is_url_like(self, text: str) -> bool:
        """
        Check if a string looks like a URL fragment or contains web-related patterns.
        Enhanced to catch any substring with 'http', 'www', or 'https', even without dots or slashes.
        """
        lowered = text.lower()
        if 'http' in lowered or 'www' in lowered or 'https' in lowered:
            return True
        url_patterns = [
            r'http[s]?://', r'www\.', r'\.com', r'\.org', r'\.net', r'\.io', r'\.gov', r'\.edu',
            r'httpswww', r'httpwww', r'httpswww', r'www', r'\.html', r'\.php', r'\.aspx', r'\.jsp', r'\.htm'
        ]
        for pattern in url_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False

    def _post_process_keywords(self, keywords: List[str]) -> List[str]:
        """
        Post-process keywords to remove repetitive terms, duplicates, malformed, URL-like, generic, numeric, non-English, and short keywords.
        Ensures proper spacing and complete words.
        Adds debug logging for filtered keywords.
        """
        MAX_KEYWORD_LENGTH = 30
        unique_keywords = []
        seen = set()
        stopwords_set = self.stop_words
        for keyword in keywords:
            cleaned = self._clean_keyword(keyword)
            if not cleaned or len(cleaned) < self.min_word_length:
                continue
            if len(cleaned) > MAX_KEYWORD_LENGTH:
                continue
            if cleaned.count('-') > 2:
                continue
            keyword_lower = cleaned.lower()
            words = keyword_lower.split()
            # Only check single-word keywords against the vocabulary
            if len(words) == 1 and words[0] not in self.english_vocab:
                continue
            # Skip n-grams that start or end with a stopword
            if words[0] in stopwords_set or words[-1] in stopwords_set:
                continue
            if keyword_lower in seen:
                continue
            if len(words) > 3:
                continue
            if len(words) > 1 and len(set(words)) < len(words):
                continue
            if any(self._is_mostly_numeric(word) for word in words):
                continue
            if len(words) == 1 and keyword_lower in ['easy', 'simple', 'quick', 'basic', 'advanced']:
                continue
            similar_exists = False
            for existing in seen:
                if (existing in keyword_lower or keyword_lower in existing or self._similarity_score(existing, keyword_lower) > 0.8):
                    similar_exists = True
                    break
            if similar_exists:
                continue
            if self._is_url_like(keyword_lower):
                continue
            if not self._is_english_only(cleaned):
                continue
            unique_keywords.append(cleaned)
            seen.add(keyword_lower)
        return unique_keywords
        
    def _similarity_score(self, str1: str, str2: str) -> float:
        """
        Calculate a simple similarity score between two strings.
        """
        # Simple word overlap measure
        words1 = set(str1.split())
        words2 = set(str2.split())
        
        if not words1 or not words2:
            return 0.0
            
        overlap = len(words1.intersection(words2))
        total = len(words1.union(words2))
        
        return overlap / total if total > 0 else 0.0

    def score_keywords_by_title_similarity(self, keywords, title, top_n=15):
        """
        Rank keywords by semantic similarity to the title/topic using embeddings.
        """
        if not keywords or not title:
            return keywords[:top_n]
        # Get embedding for the title/topic
        title_embedding = vector_store.embedding_function([title])[0]
        # Get embeddings for all keywords
        keyword_embeddings = vector_store.embedding_function(keywords)
        # Compute cosine similarity for each keyword
        similarities = []
        for i, emb in enumerate(keyword_embeddings):
            sim = np.dot(emb, title_embedding) / (np.linalg.norm(emb) * np.linalg.norm(title_embedding))
            similarities.append((keywords[i], sim))
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: -x[1])
        # Return top N keywords
        return [kw for kw, _ in similarities[:top_n]]

    def get_suggested_topics(self, content: str, max_keywords: int = 15, title: str = None) -> List[str]:
        """Get exactly max_keywords suggested topics from the content using vector store approach. Optionally prefer keywords semantically similar to title."""
        try:
            # Try vector store approach first
            keywords = self.extract_keywords_with_vector_store(content, max_keywords=max_keywords)

            # Use improved post-processing for filtering
            processed_keywords = self._post_process_keywords(keywords)

            # If title is provided, rank by semantic similarity to title
            if title:
                processed_keywords = self.score_keywords_by_title_similarity(processed_keywords, title, top_n=max_keywords)

            # Ensure exactly max_keywords keywords are returned
            if len(processed_keywords) > max_keywords:
                processed_keywords = processed_keywords[:max_keywords]
            while len(processed_keywords) < max_keywords:
                processed_keywords.append("")

            return processed_keywords
        except Exception as e:
            print(f"Vector store keyword extraction failed: {str(e)}")
            # Fall back to traditional method
            keywords = self.extract_keywords(content, max_keywords=max_keywords)

            # Use improved post-processing for filtering
            processed_keywords = self._post_process_keywords(keywords)

            # If title is provided, rank by semantic similarity to title
            if title:
                processed_keywords = self.score_keywords_by_title_similarity(processed_keywords, title, top_n=max_keywords)

            # Ensure exactly max_keywords keywords are returned
            if len(processed_keywords) > max_keywords:
                processed_keywords = processed_keywords[:max_keywords]
            while len(processed_keywords) < max_keywords:
                processed_keywords.append("")

            return processed_keywords

    def _is_mostly_numeric(self, text: str) -> bool:
        """
        Check if a string is mostly numeric (>50% digits or contains specific patterns)
        """
        if not text:
            return False
            
        # Pure numbers
        if text.isdigit():
            return True
            
        # Count digits
        digit_count = sum(1 for c in text if c.isdigit())
        
        # If more than 40% of the characters are digits, consider it numeric
        if digit_count / len(text) > 0.4:
            return True
            
        # Check for common patterns like version numbers, IDs, etc.
        numeric_patterns = [
            r'^\d+[a-z]?$',               # Like 143 or 143a
            r'^[a-z]\d+$',                # Like r143
            r'^\d+\.\d+$',               # Like 1.0
            r'^v\d+(\.\d+)*$',           # Like v1.0
            r'^\d{2,4}$'                 # 2-4 digit numbers (years, codes)
        ]
        
        for pattern in numeric_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True
                
        return False

# Create singleton instance
keyword_extractor = KeywordExtractor()
