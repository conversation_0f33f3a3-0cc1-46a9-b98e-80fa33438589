from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, current_user
from auth.user import User, get_user_annotation_mode
from auth.user_auth import UserAuth
from Modules.google_sheets import log_user_login
import logging

auth_routes = Blueprint('auth_routes', __name__)
logger = logging.getLogger('auth_routes')
user_auth = UserAuth()

# Role-based redirect mapping
ROLE_REDIRECTS = {
    'admin': 'admin_routes.dashboard',
    'auditor': 'auditor_routes.auditor_dashboard',
    'annotator': 'annotator_routes.annotator_dashboard'
}

def get_redirect_for_role(role):
    """Return the appropriate redirect URL for a given role"""
    return url_for(ROLE_REDIRECTS.get(role, 'annotator_routes.annotator_dashboard'))

@auth_routes.route('/login', methods=['POST'], endpoint='login')
def login():
    """Login API endpoint"""
    username = request.form.get('username')
    password = request.form.get('password')
    if not username or not password:
        return jsonify({'success': False, 'message': 'Please enter both username and password'}), 400

    success, result = user_auth.authenticate_user(username, password)
    if success:
        user = User(result['id'], result['username'], result['role'], result.get('full_name', ''))
        login_user(user)
        session.update({
            'username': user.username,
            'role': user.role,
            'full_name': user.full_name
        })
        logger.info(f"User {username} logged in successfully with role: {result['role']}")
        return jsonify({'success': True}), 200
    else:
        logger.warning(f"Failed login attempt for user {username}")
        return jsonify({'success': False, 'message': 'Invalid username or password'}), 401

# Logout route
@auth_routes.route('/logout', methods=['POST'])
def logout():
    """Logout API endpoint"""
    username = session.get('username', 'Unknown')
    logger.info(f"User {username} is logging out")
    logout_user()
    session.clear()
    return jsonify({'success': True}), 200

@auth_routes.route('/user-register', methods=['GET', 'POST'], endpoint='user_register')
def user_register():
    """Register a new user (standalone registration page)"""
    # Redirect non-admin authenticated users to their dashboard
    if current_user.is_authenticated and hasattr(current_user, 'role') and current_user.role != 'admin':
        return redirect(get_redirect_for_role(current_user.role))

    if request.method == 'POST':
        # Get form data
        form_data = {
            'username': request.form.get('username'),
            'password': request.form.get('password'),
            'confirm_password': request.form.get('confirm_password'),
            'role': request.form.get('role', 'annotator'),
            'full_name': request.form.get('full_name', ''),
            'email': request.form.get('email', ''),
            'annotation_mode': request.form.get('annotation_mode', 'manual')
        }

        # Basic validation
        if not all([form_data['username'], form_data['password'], form_data['confirm_password']]):
            flash('All fields are required', 'danger')
            return redirect(url_for('auth_routes.user_register'))

        if form_data['password'] != form_data['confirm_password']:
            flash('Passwords do not match', 'danger')
            return redirect(url_for('auth_routes.user_register'))

        # Only allow admin to create admin users
        is_admin = current_user.is_authenticated and hasattr(current_user, 'role') and current_user.role == 'admin'
        if form_data['role'] == 'admin' and not is_admin:
            form_data['role'] = 'annotator'  # Default to annotator if not admin
            logger.warning(f"Non-admin user attempted to create admin account: {form_data['username']}")

        # Register the user
        verification_mode = (form_data['annotation_mode'] == 'verification')
        success, message = user_auth.register_user(
            form_data['username'],
            form_data['password'],
            form_data['role'],
            form_data['full_name'],
            form_data['email'],
            form_data['annotation_mode'],
            verification_mode
        )

        if success:
            flash('User registered successfully! You can now log in.', 'success')
            # Invalidate user cache
            try:
                from cache.admin_cache import invalidate_user_cache
                invalidate_user_cache()
                logger.debug(f"Invalidated user cache after registering new user: {form_data['username']}")
            except Exception as cache_error:
                logger.warning(f"Error invalidating user cache: {str(cache_error)}")

            # Redirect based on user role
            return redirect(
                url_for('admin_routes.manage_users') if is_admin else
                url_for('auth_routes.login')
            )
        else:
            flash(f'Registration failed: {message}', 'danger')
            return redirect(url_for('auth_routes.user_register'))

    # Render appropriate template
    is_admin = current_user.is_authenticated and hasattr(current_user, 'role') and current_user.role == 'admin'
    return render_template('admin/manage_users/register.html' if is_admin else 'user_register.html')
