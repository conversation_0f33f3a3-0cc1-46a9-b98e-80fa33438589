import requests
import pdfplumber
from PyPDF2 import PdfReader
import os
import async<PERSON>
from typing import <PERSON><PERSON>
from urllib.parse import urlparse
from pathlib import Path
import logging
from .text_cleaner import text_cleaner
import urllib.robotparser
import crawl4ai  # Added import for crawl4ai
from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON>raw<PERSON>, BrowserConfig, CrawlerRunConfig, CacheMode

class ContentExtractor:
    def __init__(self):
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.supported_extensions = {'.pdf', '.txt', '.md'}
        
        # Check PyMuPDF availability at initialization
        try:
            import fitz
            # Try to create a minimal document to verify PyMuPDF is working
            try:
                doc = fitz.open()
                doc.close()
                self._pymupdf_available = True
                self.logger.info("PyMuPDF is available and working correctly")
            except Exception as e:
                self._pymupdf_available = False
                self.logger.warning(f"PyMuPDF initialization test failed: {str(e)}")
        except ImportError:
            self._pymupdf_available = False
            self.logger.warning("PyMuPDF is not installed, will use alternative PDF extraction methods")

    def check_robots_txt(self, url: str) -> Tuple[bool, str]:
        """
        Check robots.txt to see if we have permission to scrape the URL.
        
        Args:
            url: The URL to check
            
        Returns:
            Tuple of (allowed, reason)
        """
        try:
            parsed_url = urlparse(url)
            if not all([parsed_url.scheme, parsed_url.netloc]):
                return False, "Invalid URL format"
                
            # Create a robots parser
            rp = urllib.robotparser.RobotFileParser()
            
            # Set the URL for the robots.txt file
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            self.logger.info(f"Checking robots.txt at {robots_url}")
            
            # Read the robots.txt file
            rp.set_url(robots_url)
            rp.read()
            
            # Check if our user agent is allowed to fetch the URL
            user_agent = "Mozilla/5.0"  # Using a common user agent
            can_fetch = rp.can_fetch(user_agent, url)
            
            if not can_fetch:
                return False, f"Access to {url} is disallowed by the website's robots.txt rules"
                
            return True, "Access is allowed by robots.txt"
            
        except Exception as e:
            self.logger.warning(f"Error checking robots.txt: {str(e)}")
            # If we can't check robots.txt, we proceed but log the error
            return True, f"Unable to check robots.txt: {str(e)}"

    def extract_website_content(self, url: str) -> str:
        """Extract and clean text content from a website URL or a direct link to a document."""
        try:
            # Validate URL
            parsed_url = urlparse(url)
            if not all([parsed_url.scheme, parsed_url.netloc]):
                raise ValueError("Invalid URL format")
                
            # Check robots.txt first
            allowed, reason = self.check_robots_txt(url)
            if not allowed:
                raise ValueError(reason)

            # Check if the URL points to a supported document type
            url_path = parsed_url.path.lower()
            
            # Determine if this is a direct document link by file extension
            is_pdf = url_path.endswith('.pdf')
            is_txt = url_path.endswith('.txt')
            is_md = url_path.endswith('.md')
            
            # Handle document file types with direct download
            if is_pdf or is_txt or is_md:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept-Language': '*',
                    'Accept-Charset': 'UTF-8',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
                
                response = requests.get(url, headers=headers, timeout=15)
                response.raise_for_status()
                
                # Check content type from headers
                content_type = response.headers.get('Content-Type', '').lower()
                is_pdf = is_pdf or 'application/pdf' in content_type
                is_txt = is_txt or 'text/plain' in content_type
                is_md = is_md or 'text/markdown' in content_type
                
                import tempfile
                
                # Determine file extension
                if is_pdf:
                    suffix = '.pdf'
                elif is_txt:
                    suffix = '.txt'
                elif is_md:
                    suffix = '.md'
                
                # Save to a temporary file
                with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as temp_file:
                    temp_file.write(response.content)
                    temp_path = temp_file.name
                
                self.logger.info(f"Downloaded document to temporary file: {temp_path}")
                
                try:
                    # Process according to file type
                    if is_pdf:
                        result = self.extract_pdf_text(temp_path)
                    elif is_txt:
                        result = self.extract_txt_text(temp_path)
                    elif is_md:
                        result = self.extract_md_text(temp_path)
                    
                    # Clean up the temporary file
                    os.remove(temp_path)
                    self.logger.info(f"Processed document from URL: {url}")
                    return result
                    
                except Exception as doc_err:
                    # Clean up the temporary file even if processing fails
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    raise ValueError(f"Error processing document from URL: {str(doc_err)}")
            
            # Use crawl4ai for web scraping with the correct AsyncWebCrawler class
            self.logger.info(f"Using crawl4ai to extract content from: {url}")
            try:
                # Use the AsyncWebCrawler class from crawl4ai
                async def crawl_url():
                    
                    browser_config = BrowserConfig(
                        headless=True,
                        user_agent="Mozilla/5.0 (compatible; MyBot/1.0)"
                    )
                    
                    run_config = CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        check_robots_txt=False  # We already checked it
                    )
                    
                    async with AsyncWebCrawler(config=browser_config) as crawler:
                        result = await crawler.arun(
                            url=url,
                            config=run_config
                        )
                        return result.markdown.raw_markdown if hasattr(result.markdown, 'raw_markdown') else result.markdown
                
                # Run the async function to get the content
                page_content = asyncio.run(crawl_url())
                
                if not page_content:
                    raise ValueError("No content could be extracted from the URL")
                
                # Clean the extracted text
                cleaned_text = text_cleaner.clean_web_text(page_content)
                return cleaned_text
                
            except Exception as crawl_err:
                self.logger.error(f"Error using crawl4ai: {str(crawl_err)}")
                raise ValueError(f"Error extracting content with crawl4ai: {str(crawl_err)}")
            
        except Exception as e:
            self.logger.error(f"Error extracting website content: {str(e)}")
            raise ValueError(f"Error extracting website content: {str(e)}")
        
    def extract_pdf_text(self, file_path: str) -> str:
        """Extract and clean text from PDF file using multiple methods."""
        try:
            text_content = []
            
            # Method 1: PyMuPDF (best for PDFs with complex layouts)
            if self._pymupdf_available:
                try:
                    import fitz
                    doc = fitz.open(file_path)
                    if doc.is_pdf:  # Verify it's a valid PDF
                        for page in doc:
                            text = page.get_text()
                            if text and text.strip():
                                text_content.append(text.strip())
                        doc.close()
                        self.logger.info("Successfully extracted text using PyMuPDF")
                    else:
                        self.logger.warning("File is not a valid PDF according to PyMuPDF")
                except Exception as e:
                    self.logger.warning(f"PyMuPDF extraction failed: {str(e)}")
            
            # Method 2: pdfplumber (good for tables and structured content)
            if not text_content:
                try:
                    with pdfplumber.open(file_path) as pdf:
                        for page in pdf.pages:
                            text = page.extract_text()
                            if text and text.strip():
                                text_content.append(text.strip())
                    self.logger.info("Successfully extracted text using pdfplumber")
                except Exception as e:
                    self.logger.warning(f"pdfplumber extraction failed: {str(e)}")
            
            # Method 3: PyPDF2 (fallback)
            if not text_content:
                try:
                    with open(file_path, 'rb') as file:
                        reader = PdfReader(file)
                        for page in reader.pages:
                            text = page.extract_text()
                            if text and text.strip():
                                text_content.append(text.strip())
                    self.logger.info("Successfully extracted text using PyPDF2")
                except Exception as e:
                    self.logger.warning(f"PyPDF2 extraction failed: {str(e)}")
            
            if not text_content:
                raise ValueError("No text could be extracted from the PDF using any method")
            
            # Join and clean the extracted text
            raw_text = '\n\n'.join(text_content)
            cleaned_text = text_cleaner.clean_pdf_text(raw_text)
            
            return cleaned_text
            
        except Exception as e:
            self.logger.error(f"Error extracting PDF text: {str(e)}")
            raise ValueError(f"Error extracting PDF text: {str(e)}")

    def extract_txt_text(self, file_path: str) -> str:
        """Extract and clean text from TXT file with encoding detection."""
        encodings = [
            'utf-8', 'utf-8-sig', 'utf-16', 'utf-16le', 'utf-16be',
            'latin-1', 'iso-8859-1', 'cp1252', 'ascii'
        ]
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    raw_text = file.read().strip()
                    if raw_text:
                        # Clean the extracted text
                        cleaned_text = text_cleaner.clean_text(raw_text)
                        return cleaned_text
            except UnicodeDecodeError:
                continue
            except Exception as e:
                self.logger.warning(f"Error with {encoding}: {str(e)}")
                continue
        
        raise ValueError("Could not read the text file with any supported encoding")

    def extract_md_text(self, file_path: str) -> str:
        """Extract and clean text from MD file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                raw_text = file.read().strip()
                if raw_text:
                    # Clean the extracted text while preserving markdown formatting
                    cleaned_text = text_cleaner.clean_text(raw_text)
                    return cleaned_text
                else:
                    raise ValueError("Empty markdown file")
        except UnicodeDecodeError:
            # Try with a different encoding if UTF-8 fails
            with open(file_path, 'r', encoding='latin-1') as file:
                raw_text = file.read().strip()
                if raw_text:
                    cleaned_text = text_cleaner.clean_text(raw_text)
                    return cleaned_text
                else:
                    raise ValueError("Empty markdown file")
        except Exception as e:
            self.logger.error(f"Error extracting MD text: {str(e)}")
            raise ValueError(f"Error extracting MD text: {str(e)}")

    def process_document(self, file_path: str) -> str:
        """Process uploaded document and extract clean text."""
        if not os.path.exists(file_path):
            raise ValueError("File not found")
        
        try:
            extension = Path(file_path).suffix.lower()
            if extension not in self.supported_extensions:
                raise ValueError(f"Unsupported file format. Supported formats: {', '.join(self.supported_extensions)}")
            
            if extension == '.pdf':
                return self.extract_pdf_text(file_path)
            elif extension == '.txt':
                return self.extract_txt_text(file_path)
            elif extension == '.md':
                return self.extract_md_text(file_path)
            
        except Exception as e:
            self.logger.error(f"Error processing document: {str(e)}")
            raise ValueError(f"Error processing document: {str(e)}")

# Create singleton instance
extractor = ContentExtractor()