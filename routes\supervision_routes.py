import os
import json
from datetime import datetime
from flask import  Blueprint, render_template, request, redirect, url_for, session, jsonify, flash, send_file
from werkzeug.utils import secure_filename
from io import  BytesIO
from flask_login import login_required
import csv
import io
 
# Update imports to use new module structure
from auth.decorators import annotator_required
from Modules.document_processor import parse_extracted_text, process_queue, results_dict
from Modules.file_handler import delete_file, get_file_extension, allowed_file
from Modules.google_sheets import save_document_data
from Modules.export import convert_to_csv_content, download_csv as export_download_csv
from Modules.google_drive import (
    download_file_from_drive,
    get_google_drive_service,
    get_annotator_drive_proxy,
    get_folder_hierarchy,
    list_folder_contents_with_type
)
 
from core_modules.config import Config
import logging
 
logger = logging.getLogger('supervision_routes')
 
def is_image_file(filename):
    """Check if a file is an image based on its extension"""
    if not filename:
        return False
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
    file_ext = os.path.splitext(filename.lower())[1]
    return file_ext in image_extensions
 
supervision_routes = Blueprint('supervision_routes', __name__)
 
@supervision_routes.route('/dashboard')
@login_required
@annotator_required
def supervision_route():
    """Show document type selection for processing"""
    try:
 
        return render_template('annotator/supervision.html')
    except Exception as e:
        logger.error(f"Error rendering supervision.html: {str(e)}")
        flash("Error loading supervision page")
        return redirect(url_for('annotator_routes.annotator_dashboard'))
 
@supervision_routes.route('/upload', methods=['POST'])
@login_required
@annotator_required
def upload_documents():
    """Handle document uploads and queue for processing"""
    document_type = request.form.get('document_type')
    model_type = request.form.get('model_type', 'standard')  # Default to standard if not provided
 
    if not document_type or document_type not in ['passport', 'check', 'invoice']:
        return jsonify({'error': 'Invalid document type'}), 400
 
    if not model_type or model_type not in ['standard', 'enhanced', 'premium']:
        # Log warning but default to standard
        logger.warning(f"Invalid model type received: {model_type}, defaulting to standard")
        model_type = 'standard'
 
    if 'files[]' not in request.files:
        return jsonify({'error': 'No files selected'}), 400
    files = request.files.getlist('files[]')
    if not files or files[0].filename == '':
        return jsonify({'error': 'No files selected'}), 400
 
    if len(files) > 10:
        return jsonify({'error': 'You can only upload up to 10 files at a time'}), 400
 
    processed_files = []
 
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{timestamp}_{filename}"
            image_id = f"{timestamp}_{filename}"
 
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
 
            file_data = file.read()
            file_path = os.path.join(Config.UPLOAD_FOLDER, unique_filename)
 
            if file_size < 1024 * 1024 and Config.CLEANUP_AFTER_PROCESSING:
                results_dict[image_id] = {
                    'status': 'processing',
                    'path': file_path,
                    'image_data': file_data,
                    'model_type': model_type  # Store the model type
                }
                file_buffer = BytesIO(file_data)
                process_queue.put((file_buffer, document_type, image_id, True, model_type))
            else:
                with open(file_path, 'wb') as f:
                    f.write(file_data)
 
                results_dict[image_id] = {
                    'status': 'processing',
                    'path': file_path,
                    'image_data': file_data,
                    'model_type': model_type  # Store the model type
                }
                process_queue.put((file_path, document_type, image_id, False, model_type))
 
            processed_files.append({
                'id': image_id,
                'name': filename,
                'path': file_path,
                'model_type': model_type
            })
 
    session['document_type'] = document_type
    session['model_type'] = model_type  # Store model type in session
    session['processing_files'] = processed_files
 
    return jsonify({
        'success': True,
        'redirect': url_for('supervision_routes.review_documents'),
        'message': f'{len(processed_files)} files uploaded and being processed with {model_type} processing power'
    })
 
@supervision_routes.route('/review')
@login_required
@annotator_required
def review_documents():
    """Show document review page"""
    if 'document_type' not in session or 'processing_files' not in session:
        flash('No documents selected for processing')
        return redirect(url_for('supervision_routes.supervision_route'))
    # JSON API response
    if request.args.get('json') == 'true':
        return jsonify({
            'files': session.get('processing_files', []),
            'document_type': session.get('document_type'),
            'model_type': session.get('model_type', 'standard')
        })
    return render_template(
        'annotator/review.html',
        document_type=session['document_type'],
        model_type=session.get('model_type', 'standard'),
        files=session['processing_files']
    )
 
@supervision_routes.route('/api/check-status/<image_id>')
@login_required
@annotator_required
def check_status(image_id):
    """Check processing status of a document"""
    if image_id in results_dict:
        result = results_dict[image_id]
        if result['status'] == 'completed':
            # Only log completion once
            if not result.get('completion_logged', False):
                logger.info(f"Document processing completed: {image_id}")
                result['completion_logged'] = True
            # Parse the extracted text into structured data
            extracted_data = parse_extracted_text(result['data'], session['document_type'])
 
            # Prepare response
            response_data = {
                'status': 'completed',
                'data': extracted_data
            }
 
            return jsonify(response_data)
        elif result['status'] == 'error':
            # Only log error once
            if not result.get('error_logged', False):
                logger.error(f"Document processing failed: {image_id} - {result.get('error', 'Unknown error')}")
                result['error_logged'] = True
 
            # Prepare response
            response_data = {
                'status': 'error',
                'error': result.get('error', 'An unknown error occurred')
            }
 
            return jsonify(response_data)
        else:
            # Only log processing status at debug level
            logger.debug(f"Document still processing: {image_id}")
 
            # For processing status
            response_data = {'status': 'processing'}
            return jsonify(response_data)
    else:
        logger.warning(f"Document not found: {image_id}")
 
        # Status not found
        response_data = {'status': 'not_found'}
        return jsonify(response_data)
 
@supervision_routes.route('/document-image/<image_id>')
@login_required
@annotator_required
def serve_document_image(image_id):
    """Serve document image from memory or disk"""
    try:
        if image_id not in results_dict:
            logger.warning(f"Document not found in results: {image_id}")
            return jsonify({'error': 'Document not found'}), 404
 
        result = results_dict[image_id]
 
        # First try to get image from memory
        if 'image_data' in result and result['image_data']:
            _, mimetype = get_file_extension(result.get('path', ''))
            logger.debug(f"Serving in-memory image: {image_id}")
            img_io = BytesIO(result['image_data'])
            img_io.seek(0)
            # Get filename from path or use image_id
            filename = os.path.basename(result.get('path', f"{image_id}.jpg"))
            return send_file(img_io, mimetype=mimetype, as_attachment=True, download_name=filename)
 
        # If not in memory, try to get from file
        file_path = result.get('path')
        if file_path and os.path.exists(file_path):
            _, mimetype = get_file_extension(file_path)
            logger.debug(f"Serving file from disk: {image_id}")
            filename = os.path.basename(file_path)
            return send_file(file_path, mimetype=mimetype, as_attachment=True, download_name=filename)
 
        # If we have a Drive service, try to download from Drive
        if 'drive_id' in result:
            try:
                service = get_google_drive_service()
                if service:
                    file_content = download_file_from_drive(service, result['drive_id'])
                    if file_content:
                        # Store in memory for future use
                        result['image_data'] = file_content.getvalue()
                        _, mimetype = get_file_extension(result.get('path', ''))
                        # Get filename from path or use drive_id
                        filename = os.path.basename(result.get('path', f"{result['drive_id']}.jpg"))
                        return send_file(file_content, mimetype=mimetype, as_attachment=True, download_name=filename)
            except Exception as e:
                logger.error(f"Error downloading from Drive: {image_id} - {str(e)}")
 
        logger.warning(f"Image not found in any location: {image_id}")
        return jsonify({'error': 'Image not found'}), 404
 
    except Exception as e:
        logger.error(f"Error serving image {image_id}: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
 
@supervision_routes.route('/api/save-document', methods=['POST'])
@login_required
@annotator_required
def save_document():
    """Save document data with user corrections"""
    data = request.json
    image_id = data.get('image_id')
    if not image_id or not data.get('data'):
        logger.error(f"Missing required data for document: {image_id}")
        return jsonify({'success': False, 'error': 'Missing required data'}), 400
 
    document_type = session.get('document_type')
    if not document_type:
        logger.error(f"Document type not found in session: {image_id}")
        return jsonify({'success': False, 'error': 'Document type not found in session'}), 400
 
    # Check if we need to include all extracted text
    include_all_text = data.get('include_all_extracted_text', False)
 
    # Mark as completed in our results dictionary regardless of saving to sheets
    if image_id in results_dict:
        results_dict[image_id]['is_saved'] = True
        logger.info(f"Document marked as saved: {image_id}")
 
        # Store the updated data
        if 'parsed_data' in results_dict[image_id]:
            results_dict[image_id]['parsed_data'].update(data.get('data'))
 
        # If include_all_extracted_text flag is set, ensure txt_content is available
        if include_all_text and 'data' in results_dict[image_id] and 'txt_content' not in results_dict[image_id]:
            # Format the data as txt content similar to what we do in download_txt
            original_data = results_dict[image_id]['data']
            # Merge with user-supplied data
            merged_data = {**original_data, **data.get('data', {})}
 
            # Create text content format
            txt_lines = []
            for key, value in merged_data.items():
                if value:  # Only include non-empty values
                    txt_lines.append(f"{key}: {value}")
 
            # Store as txt_content for later use
            txt_content = "\n".join(txt_lines)
            results_dict[image_id]['txt_content'] = txt_content
 
            # Also update the main data with the merged data for completeness
            results_dict[image_id]['data'] = merged_data
 
        # Clean up the file if it's not already cleaned up but ensure we keep image_data
        if Config.CLEANUP_AFTER_PROCESSING and 'path' in results_dict[image_id]:
            file_path = results_dict[image_id]['path']
            # Ensure we have image data in memory before deleting the file
            if 'image_data' not in results_dict[image_id] and os.path.exists(file_path):
                try:
                    # Read the image file into memory
                    with open(file_path, 'rb') as f:
                        image_data = f.read()
                    results_dict[image_id]['image_data'] = image_data
                    # Now we can delete the file
                    delete_file(file_path)
                except Exception as e:
                    logger.error(f"Error reading image data before cleanup: {image_id} - {str(e)}")
            elif os.path.exists(file_path):
                # We already have image data, just delete the file
                delete_file(file_path)
 
    # Only save to Google Sheets if verified
    if data.get('verified', True):
        # Save to Google Sheets
        processing_files = session.get('processing_files')
        filename = processing_files[0].get('name')
        folder_id = processing_files[0].get('folder_id')
        model_type = session.get('model_type')
        save_success = save_document_data(
            filename,
            model_type,
            folder_id,
            session.get('username'),
            document_type,
            data.get('data'),
            data.get('corrections')
        )
 
        if not save_success:
            logger.error(f"Failed to save to Google Sheets: {image_id}")
            return jsonify({'success': False, 'error': 'Failed to save data to Google Sheets'}), 500
 
    logger.info(f"Document successfully saved: {image_id}")
    return jsonify({'success': True})
 
@supervision_routes.route('/download_csv/<image_id>')
@login_required
@annotator_required
def download_csv(image_id):
    """Generate and download CSV for processed document"""
    if image_id not in results_dict or results_dict[image_id]['status'] != 'completed':
        flash('No processed results found for this file', 'error')
        return redirect(url_for('supervision_routes.review_documents'))
 
    result = results_dict[image_id]
    document_type = session.get('document_type', 'document')
    filename = os.path.basename(result['path'])
 
    # Log what data we have
    logger.info(f"Preparing CSV for {image_id}, document type: {document_type}")
   
    # Get extraction data - ensure we have a proper dictionary
    extraction_data = {}
    raw_data = result.get('data', {})
   
    # Convert string data to dictionary
    if isinstance(raw_data, str):
        lines = raw_data.split('\n')
        for line in lines:
            if ':' in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    key, value = parts
                    extraction_data[key.strip()] = value.strip()
    elif isinstance(raw_data, dict):
        # Deep copy to avoid modifying the original
        extraction_data = {k: v for k, v in raw_data.items()}
    else:
        # Fallback for unexpected types
        extraction_data = {"Raw Data": str(raw_data)}
   
    # Add drive link if available
    drive_link = result.get('drive_link', '')
    if drive_link:
        extraction_data['Link to File'] = drive_link
   
    # Log the processed data
    logger.info(f"Extraction data for CSV: {extraction_data}")
   
    # Create properly formatted all_results structure
    all_results = [{
        'filename': filename,
        'extraction_data': extraction_data,
        'drive_link': drive_link
    }]
   
    # Generate filename with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_filename = f"{document_type}_{filename}_{timestamp}.csv"
   
    return export_download_csv(all_results, document_type, output_filename)
 
@supervision_routes.route('/download_txt/<image_id>')
@login_required
@annotator_required
def download_txt(image_id):
    """Generate and download TXT for processed document"""
    if image_id not in results_dict or results_dict[image_id]['status'] != 'completed':
        flash('No processed results found for this file', 'error')
        return redirect(url_for('supervision_routes.review_documents'))
 
    result = results_dict[image_id]
    document_type = session.get('document_type', 'document')
    filename = os.path.basename(result['path'])
 
    # Check if we have TXT content already or need to generate it
    if 'txt_content' not in result:
        # Generate TXT content based on document type
        txt_content = result['data']
    else:
        txt_content = result['txt_content']
 
    # Create a memory file
    mem_file = BytesIO()
    mem_file.write(txt_content.encode('utf-8'))
    mem_file.seek(0)
 
    # Generate a timestamp for the filename
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
 
    return send_file(
        mem_file,
        as_attachment=True,
        download_name=f"{document_type}_{filename}_{timestamp}.txt",
        mimetype='text/plain'
    )
 
@supervision_routes.route('/process-drive-files', methods=['POST'])
@login_required
@annotator_required
def process_drive_files():
    """Process selected files from Google Drive"""
    try:
        document_type = request.form.get('document_type')
        model_type = request.form.get('model_type', 'standard')  # Default to standard if not provided
        selected_files = json.loads(request.form.get('selected_files', '[]'))
        file_links = json.loads(request.form.get('file_links', '{}'))
        current_folder_id = request.form.get('current_folder_id')
        if not document_type or document_type not in ['passport', 'check', 'invoice']:
            return jsonify({'error': 'Invalid document type'}), 400
 
        if not model_type or model_type not in ['standard', 'enhanced', 'premium']:
            # Log warning but default to standard
            logger.warning(f"Invalid model type received: {model_type}, defaulting to standard")
            model_type = 'standard'
 
        if not selected_files:
            return jsonify({'error': 'No files selected'}), 400
 
        # Get Google Drive proxy for annotators - doesn't connect directly
        service = get_annotator_drive_proxy()
        if not service:
            return jsonify({'error': 'Drive service not available. Please contact an administrator.'}), 500
 
        processed_files = []
        for file_id in selected_files:
            # Download file from proxy (cached)
            file_content = download_file_from_drive(service, file_id)
            if not file_content:
                continue
 
            # Use file_id as name since we can't get metadata in proxy mode
            file_name = f'file_{file_id}'
            if 'name' in file_links.get(file_id, {}):
                file_name = file_links[file_id]['name']
 
            # Create unique filename
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{timestamp}_{file_name}"
            image_id = f"{timestamp}_{file_name}"
 
            # Store in results dictionary with all necessary data
            results_dict[image_id] = {
                'status': 'processing',
                'path': os.path.join(Config.UPLOAD_FOLDER, unique_filename),
                'image_data': file_content.getvalue(),
                'drive_id': file_id,  # Store Drive ID for future reference
                'drive_link': file_links.get(file_id, ''),
                'mime_type': 'application/octet-stream',  # Default mime type
                'model_type': model_type  # Store the model type
            }
 
            # Add to process queue
            process_queue.put((BytesIO(file_content.getvalue()), document_type, image_id, True, model_type))
 
            processed_files.append({
                'id': image_id,
                'name': file_name,
                'path': results_dict[image_id]['path'],
                'drive_link': file_links.get(file_id, ''),
                'folder_id': current_folder_id,
                'model_type': model_type
            })
 
        session['document_type'] = document_type
        session['model_type'] = model_type  # Store model type in session
        session['processing_files'] = processed_files
 
        return jsonify({
            'success': True,
            'redirect': url_for('supervision_routes.review_documents'),
            'message': f'{len(processed_files)} files processed from Google Drive with {model_type} processing power'
        })
 
    except Exception as e:
        logger.error(f"Error processing Drive files: {str(e)}")
        return jsonify({'error': str(e)}), 500
 
@supervision_routes.route('/list-drive-folders', methods=['POST'])
@login_required
@annotator_required
def list_folders():
    try:
        data = request.get_json() or {}
        parent_folder_id = data.get('folder_id', None)
 
        # Get Google Drive proxy for annotators - doesn't connect directly
        service = get_annotator_drive_proxy()
        if not service:
            logger.warning("Drive service not available for annotator")
            return jsonify({
                'success': False,
                'error': 'Google Drive connection is not available. Please ask an administrator to connect to Google Drive first.'
            })
 
        # Get folder hierarchy for breadcrumb navigation
        breadcrumb = []
        if parent_folder_id:
            breadcrumb = get_folder_hierarchy(service, parent_folder_id)
            logger.info(f"Retrieved breadcrumb with {len(breadcrumb)} levels for folder {parent_folder_id}")
        else:
            breadcrumb = [{"id": "", "name": "Root"}]
 
        # Get all contents (folders and files) with type indicators
        contents = list_folder_contents_with_type(service, parent_folder_id)
 
        # Separate folders and files
        folders = []
        files = []
 
        for item in contents:
            if item.get('type') == 'folder':
                folders.append(item)
            else:
                files.append(item)
 
        # Sort folders and files by name
        folders.sort(key=lambda x: x.get('name', '').lower())
        files.sort(key=lambda x: x.get('name', '').lower())
 
        # Return response with breadcrumb and separated folders/files
        logger.info(f"Successfully retrieved {len(folders)} folders and {len(files)} files for folder {parent_folder_id}")
        return jsonify({
            'success': True,
            'folders': folders,
            'files': files,
            'breadcrumb': breadcrumb,
            'current_folder_id': parent_folder_id
        })
    except Exception as e:
        logger.error(f"Error in list_folders: {str(e)}")
        return jsonify({
            'success': False,
            'error': f"Error retrieving folders: {str(e)}"
        })
 
@supervision_routes.route('/download_csv', methods=['POST'])
@login_required
@annotator_required
def download_csv_route():
    """Handle downloading edited CSV data from the form"""
    try:
        # Get data from request
        data = request.json
        if not data:
            return jsonify({'error': 'No data provided'}), 400
           
        edited_data = data.get('edited_data')
        document_type = data.get('document_type')
        document_id = data.get('document_id')
        filename = data.get('filename', 'data.csv')
       
        # Log the filename for debugging
        logger.info(f"Downloading edited CSV with filename: {filename}")
       
        if not edited_data:
            return jsonify({'error': 'No edited data provided'}), 400
           
        # Convert edited_data from object to CSV format
        field_names = edited_data.keys()
       
        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output, quoting=csv.QUOTE_MINIMAL)
       
        # Write header row
        writer.writerow(field_names)
       
        # Write data row
        row_values = [edited_data.get(field, '') for field in field_names]
        writer.writerow(row_values)
       
        # Create response
        mem = io.BytesIO()
        mem.write(output.getvalue().encode('utf-8'))
        mem.seek(0)
        output.close()
       
        # Log the action
        logger.info(f"Downloading edited CSV for document_id: {document_id}, type: {document_type}")
       
        # Make sure the Content-Disposition header is set correctly
        response = send_file(
            mem,
            mimetype='text/csv',
            as_attachment=True,
            download_name=filename
        )
       
        # Add Cache-Control header to prevent caching
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
       
        return response
       
    except Exception as e:
        logger.error(f"Error generating edited CSV: {str(e)}")
        return jsonify({'error': f'Error generating CSV: {str(e)}'}), 500
 
 