# routes/annotator_routes.py
from flask import (Flask, Blueprint, render_template, request, Response, redirect,
                  url_for, flash, session, jsonify)
from flask_login import login_required
import os, io, random, string, mimetypes, traceback, logging
from datetime import datetime
from PIL import Image

# Core modules
from core_modules.config import Config
from core_modules.nas_connector import get_ftp_connector
from auth.user import get_user_annotation_mode
from auth.decorators import annotator_required
from image_utils.database import db as image_db
from image_utils.batch_manager import get_batch_manager
from data_utils import manual_labeling, verification_mode
from cache.image_cache import get_cached_image, cache_image, is_image_cached

# Initialize blueprint and logger
annotator_routes = Blueprint('annotator_routes', __name__)
logger = logging.getLogger('annotator_routes')

# Configure Flask app
app = Flask(__name__)
app.config.from_object(Config)
app.secret_key = app.config['SECRET_KEY']
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024

# Helper functions
def normalize_path(path, url_decode=False):
    """Normalize a file path for consistent handling"""
    if not path:
        return '/'
    if url_decode:
        path = path.replace('%20', ' ')
    path = ('/' + path.replace('\\', '/')).replace('//', '/')
    return path

def generate_request_id():
    """Generate a unique request ID for tracking"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=8))

def get_image_from_storage(image_path, request_id=None, include_response_time=False):
    """Get an image from storage (cache or FTP)"""
    request_id = request_id or generate_request_id()
    start_time = datetime.now()
    image_path = normalize_path(image_path)

    def create_response(content, source):
        content_type = mimetypes.guess_type(image_path)[0] or 'application/octet-stream'
        response = Response(content, content_type=content_type)
        response.headers.update({
            'Cache-Control': 'max-age=3600',
            'X-Source': source,
            'X-Request-ID': request_id
        })
        if include_response_time:
            response.headers['X-Response-Time'] = f"{(datetime.now() - start_time).total_seconds():.3f}s"
        return response

    # Check cache first
    if is_image_cached(image_path):
        content = get_cached_image(image_path)
        if content:
            logger.info(f"[{request_id}] Serving image from cache: {image_path}")
            return create_response(content, 'Redis-Cache')

    # Get from FTP
    logger.info(f"[{request_id}] Retrieving image from FTP: {image_path}")
    ftp_connector = get_ftp_connector()
    if not ftp_connector:
        logger.error(f"[{request_id}] Failed to get FTP connector")
        return "Failed to connect to storage", 500

    content = ftp_connector.get_file_content(image_path)
    if content:
        cache_image(image_path, content)
        logger.info(f"[{request_id}] Serving image from FTP: {image_path} ({len(content)} bytes)")
        return create_response(content, 'FTP')

    logger.error(f"[{request_id}] Image not found on FTP: {image_path}")
    return "Image not found", 404


# Annotator Dashboard route
@annotator_routes.route('/annotator-dashboard')
@login_required
@annotator_required
def annotator_dashboard():
    username = session.get('username')
    annotation_mode = get_user_annotation_mode(username)
    admin_instructions = image_db.get_admin_instructions(annotation_mode)
    # Return JSON for API consumer if requested
    if request.args.get('json') == 'true':
        return jsonify({
            'annotation_mode': annotation_mode,
            'admin_instructions': admin_instructions
        })
    return render_template(
        'annotator/annotator_dashboard.html',
        annotation_mode=annotation_mode,
        admin_instructions=admin_instructions
    )

# Annotation route for annotators
@annotator_routes.route('/annotate')
@login_required
@annotator_required
def annotate_route():
    """Annotation interface route that assigns sequential images to annotators."""
    username = session.get('username')
    session_id = request.args.get('session_id')
    annotation_mode = get_user_annotation_mode(username)
    logger.info(f"User {username} mode: {annotation_mode}")
    admin_instructions = image_db.get_admin_instructions(annotation_mode)
    # JSON API response
    if request.args.get('json') == 'true':
        if annotation_mode == 'verification':
            imgs, labels, _, batch_name = verification_mode.handle_verification_route(username)
            images_data = verification_mode.prepare_image_data(imgs, labels)
        else:
            selected, _, batch_name = manual_labeling.get_batch_for_user(username, get_ftp_connector())
            images_data = manual_labeling.prepare_image_data(selected)
        # generate session_id if needed
        if not session_id and images_data:
            first = os.path.splitext(images_data[0]['name'])[0]
            session_id = f"{username}_{first}_{datetime.now():%Y%m%d_%H%M%S}"
        return jsonify({
            'images': images_data,
            'verification_mode': annotation_mode == 'verification',
            'session_id': session_id,
            'batch_name': batch_name,
            'admin_instructions': admin_instructions
        })
    if annotation_mode == 'verification':
        try:
            imgs, labels, current_batch, batch_name = verification_mode.handle_verification_route(username)
            if not imgs and not current_batch:
                return redirect(url_for('user_routes.no_tasks'))

            if not session_id and imgs:
                first_name = os.path.splitext(imgs[0]['name'])[0] if imgs else 'empty'
                session_id = f"{username}_{first_name}_{datetime.now():%Y%m%d_%H%M%S}"

            return render_template(
                'annotator/annotate.html',
                images=verification_mode.prepare_image_data(imgs, labels),
                session_id=session_id,
                user=session,
                admin_instructions=admin_instructions,
                verification_mode=True,
                batch_name=batch_name
            )
        except Exception as vm_err:
            logger.error(f"Verification mode failed: {vm_err}", exc_info=True)
            flash(f"Verification error: {vm_err}", 'danger')

    # Default to manual labeling
    return manual_labeling.handle_annotation_route(
        username,
        admin_instructions=admin_instructions,
        session_id=session_id
    )

@annotator_routes.route('/annotate/next-set')
@login_required
@annotator_required
def get_next_image_set():
    """Get the next set of images for annotation"""
    username = session.get('username')
    annotation_mode = get_user_annotation_mode(username)
    mode = "verification" if annotation_mode == 'verification' else "manual"

    logger.info(f"Getting next image set for user {username} in {mode} mode")
    batch_manager = get_batch_manager()

    # Check if user already has an assigned batch
    if current_batch := batch_manager.get_user_current_batch(username, mode=mode):
        batch_count = len(current_batch.get('images', []))
        logger.info(f"User {username} already has an assigned {mode} batch with {batch_count} images")
        action = "verify" if mode == "verification" else "annotate"
        flash(f'You still have images to {action} in your current batch. Please complete them first.', 'warning')
        return redirect(url_for('annotator_routes.annotate_route'))

    # Assign a new batch to the user
    success, message, _ = batch_manager.assign_batch_to_user(username, mode=mode)

    if not success:
        logger.error(f"Failed to assign batch to user {username} in {mode} mode: {message}")
        flash(f'No new batches available: {message}', 'info')
        return redirect(url_for('user_routes.no_tasks'))

    logger.info(f"Successfully assigned batch to user {username} in {mode} mode: {message}")
    flash(f'New batch assigned: {message}', 'success')
    return redirect(url_for('annotator_routes.annotate_route'))

# API route to get an image
@annotator_routes.route('/api/image/<path:image_path>')
@login_required
def get_image(image_path):
    """API route to get an image"""
    request_id = generate_request_id()
    try:
        return get_image_from_storage(image_path, request_id, include_response_time=True)
    except Exception as e:
        logger.error(f"[{request_id}] Error in get_image route: {str(e)}")
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"[{request_id}] Stack trace: {traceback.format_exc()}")
        return str(e), 500

@annotator_routes.route('/save-labels', methods=['POST'])
@login_required
@annotator_required
def save_labels():
    """Save labels for a session"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        labels = data.get('labels', {})
        is_verification_mode = data.get('verification_mode', False)
        is_complete = data.get('is_complete', False)
        request_id = data.get('request_id', '')
        batch_name = data.get('batch_name')
        username = session.get('username')

        if not session_id or not labels:
            error = 'No session ID provided' if not session_id else 'No labels provided'
            return jsonify({'success': False, 'error': error})

        logger.info(f"Saving {len(labels)} labels for user {username}, session {session_id}, "
                   f"verification_mode={is_verification_mode}, is_complete={is_complete}, request_id={request_id}")

        # Handle completion lock to prevent race conditions
        completion_lock_key = f"completion_lock_{username}_{session_id}"
        if is_complete and completion_lock_key in session:
            logger.warning(f"Duplicate completion request for user {username}, session {session_id}")
            return jsonify({
                'success': False,
                'error': 'A batch completion is already in progress. Please wait.'
            })

        if is_complete:
            session[completion_lock_key] = request_id
        try:
            # Save labels based on mode
            save_func = verification_mode.save_verified_labels if is_verification_mode else manual_labeling.save_labels
            # Pass batch_name to both verification and manual mode save functions
            success = save_func(labels, session_id, username, is_complete, batch_name)

            if success:
                logger.info(f"Successfully saved {'verification' if is_verification_mode else 'manual'} labels for user {username}")
                return jsonify({'success': True})
            else:
                logger.error(f"Failed to save {'verification' if is_verification_mode else 'manual'} labels for user {username}")
                return jsonify({'success': False, 'error': f"Failed to save {'verification' if is_verification_mode else 'manual'} labels"})
        finally:
            if is_complete and completion_lock_key in session:
                session.pop(completion_lock_key, None)
    except Exception as e:
        logger.error(f"Error in save_labels route: {str(e)}")
        # Clear any locks in case of exception
        if 'completion_lock_key' in locals() and is_complete and completion_lock_key in session:
            session.pop(completion_lock_key, None)
        return jsonify({'success': False, 'error': f'Failed to save labels: {str(e)}'})

@annotator_routes.route('/api/save-cropped-image', methods=['POST'])
@login_required
@annotator_required
def api_save_cropped_image():
    """API route to save a cropped image, overriding the original file"""
    request_id = generate_request_id()

    try:
        # Validate request parameters
        if 'image' not in request.files or 'image_path' not in request.form or not request.form['image_path']:
            logger.error(f"[{request_id}] Missing or invalid parameters in save-cropped-image request")
            return jsonify({'success': False, 'error': 'Missing or invalid parameters'}), 400

        image_file = request.files['image']
        image_path = normalize_path(request.form['image_path'], url_decode=True)
        logger.info(f"[{request_id}] Processing cropped image for: {image_path}")

        # Read and process image
        image_binary = image_file.read()

        try:
            # Validate and convert image if needed
            img = Image.open(io.BytesIO(image_binary))
            original_format = os.path.splitext(image_path)[1].lower().replace('.', '')

            if original_format and original_format != img.format.lower():
                output = io.BytesIO()
                save_format = 'jpeg' if original_format == 'jpg' else original_format
                img.save(output, format=save_format.upper())
                image_binary = output.getvalue()
            img.close()
        except Exception as e:
            logger.error(f"[{request_id}] Invalid image data: {str(e)}")
            return jsonify({'success': False, 'error': f'Invalid image data: {str(e)}'}), 400

        # Save to FTP
        ftp_connector = get_ftp_connector()
        if not ftp_connector:
            logger.error(f"[{request_id}] Failed to get FTP connector")
            return jsonify({'success': False, 'error': 'No FTP connection available'}), 500

        # Try to delete original file (ignore errors)
        try:
            ftp_connector.delete_file(image_path)
        except Exception:
            pass
        # Save the new file
        if ftp_connector.save_file(image_path, image_binary):
            cache_image(image_path, image_binary)
            return jsonify({
                'success': True,
                'message': 'Cropped image saved successfully',
                'path': image_path
            })
        else:
            logger.error(f"[{request_id}] Failed to save image with FTP connector")
            return jsonify({'success': False, 'error': 'Failed to save image'}), 500

    except Exception as e:
        logger.error(f"[{request_id}] Error in save-cropped-image API: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

########################################################
# AUDITOR IMAGE ROUTES
########################################################

@annotator_routes.route('/api/get-nas-image')
def get_nas_image():
    """Get an image from NAS path"""
    try:
        nas_path = request.args.get('nas_path')
        if not nas_path:
            logger.error("No NAS path provided in the request")
            return jsonify({"error": "No NAS path provided"}), 400

        request_id = generate_request_id()
        result = get_image_from_storage(nas_path, request_id)

        # Convert error tuple to JSON response if needed
        if isinstance(result, tuple) and len(result) == 2 and isinstance(result[1], int):
            return jsonify({"error": result[0]}), result[1]

        return result
    except Exception as e:
        logger.error(f"Error in get_nas_image: {str(e)}")
        return jsonify({"error": str(e)}), 500
