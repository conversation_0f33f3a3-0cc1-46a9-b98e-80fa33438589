# Backend Migration Summary

## Overview
This document summarizes the changes made to migrate the Flask backend from serving HTML templates to providing API-only endpoints for the Next.js frontend.

## Key Changes Made

### 1. Flask App Configuration (`app.py`)

#### CORS Configuration
- Updated CORS settings to allow requests from Next.js frontend (localhost:3000)
- Added support for both localhost and 127.0.0.1

#### Blueprint Registration
- Added `/api` prefix to all route blueprints:
  - `auth_routes` → `/api/auth`
  - `admin_routes` → `/api/admin`
  - `annotator_routes` → `/api/annotator`
  - `auditor_routes` → `/api/auditor`
  - `user_routes` → `/api/users`
  - `supervision_routes` → `/api/supervision`
  - `fetch_data` → `/api/fetch-data`
  - `synthetic_dataset_bp` → `/api/synthetic`
  - `extractor_routes` → `/api/extractor`

#### Route Updates
- **Index Route (`/`)**: Now redirects to Next.js frontend (localhost:3000)
- **Session API (`/api/session`)**: New endpoint to get current user session info
- **Legacy Routes**: Updated to redirect to corresponding Next.js pages
- **Static Files**: Pattern background still served by Flask for compatibility

### 2. Authentication Routes (`routes/auth_routes.py`)

#### Login Endpoint (`/api/auth/login`)
- Enhanced to handle both JSON and form data
- Returns user information in response upon successful login
- Maintains session-based authentication

#### Registration Endpoint (`/api/auth/register`)
- Converted from template-rendering to API-only
- Handles both JSON and form data
- Returns JSON responses for all scenarios

### 3. Admin Routes (`routes/admin_routes.py`)

#### Dashboard (`/api/admin/dashboard`)
- Converted from template rendering to JSON API
- Returns configuration data and OAuth callback status
- Maintains all functionality while providing structured data

#### OCR Directory (`/api/admin/ocr-directory`)
- Removed template rendering logic
- Returns comprehensive directory and configuration data as JSON
- Preserves caching functionality

### 4. Next.js Frontend Updates (`nextjs-frontend/`)

#### API Client (`lib/api-client.ts`)
- Updated base URL to point to Flask backend on port 8080
- Added `withCredentials: true` for session management
- Updated all API endpoints to use new `/api` prefix structure
- Added new authentication endpoints (register, session)
- Organized endpoints by functionality (admin, auth, users, etc.)

#### Next.js Configuration (`next.config.js`)
- Updated image remote patterns to use port 8080
- Added API proxy rewrites to forward `/api/*` requests to Flask backend
- Added static file proxy for pattern background

## Port Configuration
- **Flask Backend**: Port 8080 (unchanged)
- **Next.js Frontend**: Port 3000 (default)

## Authentication Flow
1. Next.js frontend sends login request to `/api/auth/login`
2. Flask backend validates credentials and creates session
3. Session cookies are shared between frontend and backend
4. Frontend can check authentication status via `/api/session`

## API Response Format
All API endpoints now return standardized JSON responses:
```json
{
  "success": true/false,
  "message": "Optional message",
  "data": "Response data"
}
```

## Testing
Created `test_migration.py` script to verify:
- API endpoints return JSON responses
- Authentication endpoints work correctly
- Next.js frontend is accessible
- Root redirect functions properly

## Next Steps

### Immediate Actions Required
1. **Start Flask Backend**: `python app.py` (runs on port 8080)
2. **Start Next.js Frontend**: `cd nextjs-frontend && npm run dev` (runs on port 3000)
3. **Run Tests**: `python test_migration.py`

### Remaining Work
1. **Complete Route Migration**: Some admin routes may still need conversion
2. **Update Frontend Components**: Ensure all Next.js components use the new API client
3. **Error Handling**: Implement comprehensive error handling in frontend
4. **Authentication State**: Implement proper authentication state management in Next.js

### Benefits Achieved
- ✅ Clean separation between frontend and backend
- ✅ API-first architecture
- ✅ Scalable and maintainable codebase
- ✅ Modern development workflow
- ✅ Preserved existing functionality

## File Structure
```
project/
├── app.py                          # Flask app with API routes
├── routes/                         # Backend API routes
│   ├── auth_routes.py             # Authentication APIs
│   ├── admin_routes.py            # Admin APIs
│   └── ...
├── nextjs-frontend/               # Next.js frontend
│   ├── lib/api-client.ts          # API client configuration
│   ├── next.config.js             # Next.js configuration
│   └── ...
├── test_migration.py              # Migration test script
└── MIGRATION_SUMMARY.md           # This document
```

The migration successfully transforms the application from a traditional Flask template-based app to a modern API-driven architecture with Next.js frontend.
