from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify
from flask_login import login_required
import json
from image_utils.database import db as image_db
import os
import logging
from auditor import get_dashboard_data, get_verifiers_for_dataset, normalize_path, get_audit_history
from auth.decorators import auditor_required
from core_modules.nas_connector import get_ftp_connector
from cache.auditor_cache import (
    cache_available_tasks, get_cached_available_tasks,
    cache_verifiers, get_cached_verifiers,
    cache_image_folders, get_cached_image_folders,
    invalidate_verification_files_cache
)

auditor_routes = Blueprint('auditor_routes', __name__)
logger = logging.getLogger('auditor_routes')

# Helper functions
def json_error(message, status=500):
    """Return a JSON error response"""
    return jsonify({'success': False, 'message': message}), status

def json_success(data=None):
    """Return a JSON success response"""
    response = {'success': True}
    if data:
        response.update(data)
    return jsonify(response)

def get_from_cache_or_fetch(cache_getter, cache_setter, fetch_func, refresh=False, *args, **kwargs):
    """Generic function to get data from cache or fetch it"""
    if not refresh:
        cached_data = cache_getter(*args, **kwargs)
        if cached_data is not None:
            return cached_data

    data = fetch_func(*args, **kwargs)

    # Only attempt to cache if data is not None
    if data is not None:
        try:
            cache_setter(data, *args, **kwargs)
        except Exception as e:
            logger.warning(f"Error caching data: {str(e)}")
    else:
        logger.warning(f"Not caching None result from {fetch_func.__name__}")

    return data

def get_audit_folder(verification_mode=None):
    """Get the appropriate audit folder path based on verification mode"""
    if verification_mode == 'manual':
        folder = 'Data/DATP Datasets/AUDITING/Manual Task verification'
    elif verification_mode == 'agentic':
        folder = 'Data/DATP Datasets/AUDITING/Verification Task'
    else:
        folder = 'Data/DATP Datasets/AUDITING'
    return normalize_path(folder)

# Auditor routes
@auditor_routes.route('/auditor/dashboard')
@login_required
@auditor_required
def auditor_dashboard():
    """Render the auditor dashboard"""
    try:
        return render_template('auditor/dashboard.html')
    except Exception as e:
        logger.error(f"Error in auditor dashboard: {str(e)}")
        flash('An error occurred while loading the auditor dashboard.', 'danger')
        return redirect(url_for('home'))

@auditor_routes.route('/auditor/history')
@login_required
@auditor_required
def auditor_history():
    """Render the auditor history page"""
    try:
        username = session.get('username')
        history = get_audit_history(auditor_username=username)
        logger.info(f"Retrieved {len(history)} history entries for auditor {username}")
        # JSON API response
        if request.args.get('json') == 'true':
            return jsonify({'history_entries': history})
        return render_template('auditor/history.html', history_entries=history)
    except Exception as e:
        logger.error(f"Error loading audit history: {str(e)}")
        flash('Failed to load audit history. Please try again later.', 'error')
        return redirect(url_for('auditor_routes.auditor_dashboard'))

@auditor_routes.route('/api/get_verifiers', methods=['POST'])
@login_required
@auditor_required
def get_verifiers():
    """API endpoint to get verifiers for a dataset"""
    if session.get('role') != 'auditor':
        return json_error('Unauthorized', 403)

    try:
        dataset_id = request.form.get('dataset_id')
        verification_mode = request.form.get('mode')
        refresh = request.form.get('refresh', '0') == '1'

        if not dataset_id:
            return json_error('Missing dataset ID', 400)

        # Get verifiers from cache or fetch them
        verifiers = get_from_cache_or_fetch(
            get_cached_verifiers,
            cache_verifiers,
            get_verifiers_for_dataset,
            refresh,
            dataset_id,
            verification_mode
        )

        return json_success({'verifiers': verifiers})
    except Exception as e:
        logger.error(f"Error getting verifiers: {str(e)}")
        return json_error(str(e), 500)


@auditor_routes.route('/auditor/available-tasks')
@login_required
@auditor_required
def auditor_available_tasks():
    """Render the available tasks page for auditors"""
    try:
        verification_mode = request.args.get('mode')
        username = session.get('username')
        refresh = request.args.get('refresh', '0') == '1'

        logger.info(f"Auditor available tasks page loaded with verification_mode={verification_mode}")

        # Get dashboard data from cache or fetch it
        dashboard_data = get_from_cache_or_fetch(
            get_cached_available_tasks,
            cache_available_tasks,
            get_dashboard_data,
            refresh,
            username,
            verification_mode
        )

        logger.info(f"Dashboard data loaded, found {len(dashboard_data.get('datasets', []))} datasets")

        # JSON API response
        if request.args.get('json') == 'true':
            return jsonify({
                'datasets': dashboard_data.get('datasets', []),
                'stats': dashboard_data.get('stats', {})
            })
        return render_template('auditor/available_tasks.html',
                             datasets=dashboard_data.get('datasets', []),
                             stats=dashboard_data.get('stats', {}))
    except Exception as e:
        logger.error(f"Error loading available tasks page: {str(e)}")
        flash('An error occurred while loading the available tasks page.', 'danger')
        return redirect(url_for('auditor_routes.auditor_dashboard'))

@auditor_routes.route('/auditor/api/image-folders', methods=['GET'])
@login_required
@auditor_required
def get_image_folders():
    """Get available image folders from NAS"""
    try:
        refresh = request.args.get('refresh', '0') == '1'

        # Try to get from cache first
        if not refresh:
            cached_folders = get_cached_image_folders()
            if cached_folders is not None:
                logger.debug("Using cached image folders")
                return json_success({'folders': cached_folders})

        # Fetch folders from NAS
        connector = get_ftp_connector()
        if not connector:
            return json_error('Could not connect to NAS')
        auditor_image_folder = image_db.get_auditor_image_folder()
        base_path = normalize_path(auditor_image_folder)
        logger.info(f"Attempting to access image folder: {base_path}")

        folders = []
        if connector.directory_exists(base_path):
            items = connector.list_directory(base_path)
            if items:
                for item in items:
                    if isinstance(item, dict) and item.get('type') == 'directory':
                        folder_path = normalize_path(f"{base_path}/{item.get('name')}")
                        folders.append({
                            'id': folder_path,
                            'name': item.get('name')
                        })
                logger.info(f"Found {len(folders)} folders in {base_path}")
        else:
            logger.error(f"Configured path {base_path} doesn't exist")

        # Cache the result
        try:
            cache_image_folders(folders)
        except Exception as e:
            logger.warning(f"Error caching image folders: {str(e)}")

        return json_success({'folders': folders})
    except Exception as e:
        logger.error(f"Error getting image folders: {str(e)}")
        return json_error(str(e))

# The /submit-verification route has been updated to remove audit queue functionality
@auditor_routes.route('/submit-verification', methods=['POST'])
@login_required
@auditor_required
def submit_verification():
    """Handle submission of verification results"""
    try:
        data = request.get_json()

        if not data or 'task_id' not in data or 'status' not in data:
            return json_error("Invalid request data")

        logger.info(f"Verification submitted for task {data['task_id']} with status {data['status']}")
        return json_success({"message": "Verification submitted successfully"})
    except Exception as e:
        logger.error(f"Error submitting verification: {str(e)}")
        return json_error(f"Error: {str(e)}")

@auditor_routes.route('/auditor/api/verification-files/<dataset_id>/<verifier_id>')
@login_required
@auditor_required
def get_verification_files(dataset_id, verifier_id):
    """Get verification files for a specific dataset and verifier with processing status"""
    try:
        verification_mode = request.args.get('mode')
        bypass_cache = request.args.get('_t') is not None  # Check for timestamp parameter

        # Invalidate cache if requested
        if bypass_cache:
            try:
                invalidate_verification_files_cache(dataset_id, verifier_id)
                logger.info(f"Cache bypassed for verification files: {dataset_id}/{verifier_id}")
            except Exception as e:
                logger.warning(f"Error invalidating cache: {str(e)}")

        # Get the appropriate audit folder path
        audit_folder = get_audit_folder(verification_mode)
        json_files = []

        connector = get_ftp_connector()
        if connector:
            dataset_verifier_path = normalize_path(f"{audit_folder}/{dataset_id}/{verifier_id}")
            if connector.directory_exists(dataset_verifier_path):
                nas_files = connector.list_directory(dataset_verifier_path)
                if nas_files:
                    # Get processed files from audit_history table
                    processed_files = get_audit_history(dataset=dataset_id, verifier=verifier_id)
                    processed_file_names = {record['file_name'] for record in processed_files}

                    # Filter for JSON files and check processing status
                    for item in nas_files:
                        if isinstance(item, dict) and item.get('type') == 'file' and item.get('name', '').endswith('.json'):
                            file_name = item.get('name')
                            file_path = normalize_path(f"{dataset_verifier_path}/{file_name}")
                            json_files.append({
                                'name': file_name,
                                'path': file_path,
                                'status': 'Processed' if file_name in processed_file_names else 'Not Processed'
                            })

        # Create response with cache control headers
        response = jsonify({
            'success': True,
            'files': json_files
        })

        # Add cache control headers to prevent caching
        response.headers.update({
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache',
            'Expires': '0'
        })

        return response
    except Exception as e:
        logger.error(f"Error getting verification files: {str(e)}")
        return json_error('Error loading verification files')

@auditor_routes.route('/auditor/api/json-content', methods=['POST'])
@login_required
@auditor_required
def get_json_content():
    """Get the content of a JSON file and load corresponding images"""
    try:
        json_path = request.form.get('json_path')
        image_folder = request.form.get('image_folder')

        if not json_path:
            return json_error('No JSON file path provided')

        connector = get_ftp_connector()
        if not connector:
            return json_error('Could not connect to NAS')

        # Get JSON content
        content = connector.get_file_content(json_path)
        if not content:
            return json_error('Could not read JSON file')

        try:
            json_data = json.loads(content.decode('utf-8'))
            tasks = []

            # Default OCR images path
            default_path = "Data/DATP Datasets/Test and Processing/OCR IMAGES"

            # Process each image in the JSON
            for img_name, label in json_data.items():
                # Use provided image folder or default
                image_path = f"{image_folder}/{img_name}" if image_folder else f"{default_path}/{img_name}"

                # Add task if image exists
                if connector.file_exists(image_path):
                    tasks.append({
                        'task_id': os.path.splitext(img_name)[0],
                        'image_path': image_path,
                        'labels': label,
                        'status': 'pending'
                    })

            return json_success({'tasks': tasks})
        except json.JSONDecodeError:
            return json_error('Invalid JSON format')

    except Exception as e:
        logger.error(f"Error processing JSON file: {str(e)}")
        return json_error(f"Error: {str(e)}")

def add_audit_record(file_name, verifier, dataset, processed_by, status="approved", comments=""):
    """Add a new audit record to the history database or update an existing one."""
    try:
        from auditor.auditor import add_audit_history_record
        from databases.db_connector import get_db_connection

        conn = get_db_connection()
        cursor = conn.cursor()

        success = add_audit_history_record(
            file_name=file_name,
            verifier=verifier,
            dataset=dataset,
            processed_by=processed_by,
            status=status,
            comments=comments,
        )

        if not success:
            logger.error("Failed to add audit record to audit_history table")
            conn.close()
            return False

        # Increment audited_batch
        cursor.execute(
            '''
            UPDATE dataset
            SET audited_batch = audited_batch + 1
            WHERE dataset_name = ?
            ''',
            (dataset,)
        )
        conn.commit()
        conn.close()
        return True

    except Exception as e:
        logger.error(f"Error adding audit record: {str(e)}")
        return False


@auditor_routes.route('/auditor/api/save-labels', methods=['POST'])
@login_required
@auditor_required
def save_updated_labels():
    """Save updated labels to a JSON file in the VERIFIED DATASET directory"""
    try:
        data = request.get_json()
        logger.info(f"Received save-labels request with data keys: {list(data.keys()) if data else 'None'}")

        if not data or 'tasks' not in data or 'folder_name' not in data:
            logger.warning(f"Missing required data. Got: {list(data.keys()) if data else 'None'}")
            return json_error('Missing required data')

        # Extract request data
        tasks = data['tasks']
        folder_name = data['folder_name']
        original_json_path = data.get('json_path', '')
        comments = data.get('comments', '')
        verifier = data.get('verifier', 'unknown')
        username = session.get('username', 'unknown')

        # Process folder name
        if folder_name.startswith('/'):
            folder_name = os.path.basename(folder_name.rstrip('/'))

        # Build output data from tasks
        output_data = {}
        for task in tasks:
            if 'image_path' in task:
                img_filename = os.path.basename(task['image_path'])
                output_data[img_filename] = task['labels']
            elif 'labels' in task:
                task_id = task.get('task_id', 'unknown')
                logger.warning(f"Task missing image_path, using task_id as fallback: {task_id}")
                output_data[f"{task_id}.jpg"] = task['labels']

        # Determine filename
        filename = os.path.basename(original_json_path) if original_json_path and '/' in original_json_path else "verified_labels.json"

        # Set up paths
        target_dir = normalize_path(f"Data/DATP Datasets/VERIFIED DATASET/{folder_name}")
        target_path = normalize_path(f"{target_dir}/{filename}")

        # Convert data to JSON
        json_content = json.dumps(output_data, indent=2, ensure_ascii=False).encode('utf-8')

        # Get connector
        connector = get_ftp_connector()
        if not connector:
            return json_error('Could not connect to NAS')

        # Ensure directory exists
        if not connector.directory_exists(target_dir):
            logger.info(f"Creating directory: {target_dir}")
            if not connector.create_directory(target_dir):
                return json_error(f'Failed to create directory: {target_dir}')

        # Save the file
        logger.info(f"Saving file to: {target_path}")
        if not connector.save_file(target_path, json_content):
            return json_error('Failed to save file')

        # Add audit record - will update existing pending record if found
        add_audit_record(
            file_name=filename,
            verifier=verifier,
            dataset=folder_name,
            processed_by=username,
            status="approved",
            comments=comments,
        )
        # Invalidate cache
        try:
            invalidate_verification_files_cache(folder_name, verifier)
        except Exception as e:
            logger.warning(f"Error invalidating cache: {str(e)}")

        return json_success({
            'saved_path': target_path,
            'message': 'Labels saved successfully'
        })
    except Exception as e:
        logger.error(f"Error saving labels: {str(e)}")
        logger.exception("Exception stack:")
        return json_error(f'Error: {str(e)}')